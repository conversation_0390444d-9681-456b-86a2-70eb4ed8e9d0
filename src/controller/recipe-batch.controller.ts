import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeStepsStatus } from "../models/RecipeSteps";
import { RecipeResourceStatus } from "../models/RecipeResources";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { generateUniqueSlug } from "../helper/slugGenerator";
import { RecipeIngredientsNutritionCuisineData } from "../types/recipe-batch.types";

// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;
const RecipeSteps = db.RecipeSteps;
const RecipeResources = db.RecipeResources;
const RecipeHistory = db.RecipeHistory;

import {
  RECIPE_FILE_UPLOAD_CONSTANT
} from "../helper/common";
import { createRecipeHistory } from "../helper/recipe.helper";
import {
  updateRecipeCostTimestamp,
  updateRecipeNutritionTimestamp,
} from "../helper/timestamp.helper";
import {
  TransactionManager,
  FileOperationTracker,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import uploadService from "../helper/upload.service";

// ============================================================================
// CONSTANTS AND SHARED UTILITIES
// ============================================================================

// Constants for batch processing
const STEPS_BATCH_SIZE = 5;
const UPLOADS_BATCH_SIZE = 5;

// Helper function to check if user can create/update recipes
const canCreateUpdateRecipes = (userRole: any): boolean => {
  if (!userRole) return false;

  const ADMIN_SIDE_USER = ["admin", "chef", "kitchen_manager", "recipe_manager"];
  return ADMIN_SIDE_USER.includes(userRole.role_name);
};

// Common authorization check for batch operations
const checkBatchAuthorization = (req: Request, res: Response) => {
  const userId = (req as any).user?.id;
  const organizationId = (req as any).user?.organization_id;
  const userRole = (req as any).user?.roles?.[0];

  if (!userId) {
    return {
      error: res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: "Unauthorized access",
      }),
      userId: null,
      organizationId: null,
      userRole: null
    };
  }

  if (!canCreateUpdateRecipes(userRole)) {
    return {
      error: res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      }),
      userId: null,
      organizationId: null,
      userRole: null
    };
  }

  return {
    error: null,
    userId,
    organizationId,
    userRole
  };
};

// Common recipe validation
const validateRecipeAccess = async (recipeId: number, organizationId: string, transaction: any) => {
  return await Recipe.findOne({
    where: {
      id: recipeId,
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    },
    transaction,
  });
};

/**
 * @description Handle the basic information step of recipe creation
 * @route POST /api/v1/recipes/batch/basic-info
 * @access Private
 */
const createRecipeBasicInfo = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_serve_in,
      recipe_complexity_level,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_placeholder,
      categories,
      dietary_attributes
    } = sanitizedBody;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const userRole = (req as any).user?.roles?.[0];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: "Unauthorized access",
      });
    }

    // Permission check: Only certain roles can create recipes
    if (!canCreateUpdateRecipes(userRole)) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate required fields
    if (!recipe_title) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe title is required",
      });
    }

    // Generate unique slug from recipe title
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existingRecipe = await Recipe.findOne({
        where: {
          recipe_slug: slug,
          organization_id: organizationId,
          recipe_status: {
            [Op.not]: RecipeStatus.deleted,
          },
        },
      });
      return !!existingRecipe;
    };

    // Generate unique slug
    const recipe_slug = await generateUniqueSlug(
      recipe_title,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Create main recipe with initial timestamps
    const currentTimestamp = new Date();
    const recipeData = {
      // Basic info
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility: has_recipe_public_visibility || false,
      has_recipe_private_visibility: has_recipe_private_visibility || false,
      recipe_status: recipe_status || RecipeStatus.draft,
      recipe_complexity_level,

      // Serving details
      recipe_serve_in,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,

      recipe_placeholder,
      recipe_slug,
      ingredient_costs_updated_at: currentTimestamp,
      nutrition_values_updated_at: currentTimestamp,
      is_ingredient_cooking_method:
        is_ingredient_cooking_method === "true" ||
        is_ingredient_cooking_method === true,
      is_preparation_method:
        is_preparation_method === "true" || is_preparation_method === true,
      is_cost_manual:
        is_cost_manual === "true" || is_cost_manual === true,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    };

    const newRecipe = await Recipe.create(recipeData, { transaction });

    // Add categories if provided
    if (categories && Array.isArray(categories) && categories.length > 0) {
      const categoryData = categories.map((categoryId: number) => ({
        recipe_id: newRecipe.id,
        category_id: categoryId,
        status: "active",
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await db.RecipeCategory.bulkCreate(categoryData, { transaction });
    }



    // Create recipe attributes (optional for draft) - Type-wise handling
    const allAttributeData: any[] = [];

    // Handle dietary attributes (simple IDs)
    if (dietary_attributes && dietary_attributes.length > 0) {
      const dietaryData = dietary_attributes.map((attrId: number) => ({
        recipe_id: newRecipe.id,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...dietaryData);
    }
    // Bulk create all attributes if any exist
    if (allAttributeData.length > 0) {
      await RecipeAttributes.bulkCreate(allAttributeData, { transaction });
    }

    // Create recipe history entry for basic info
    await createRecipeHistory({
      recipe_id: newRecipe.id,
      action: RecipeHistoryAction.created,
      description: `Recipe "${recipe_title}" was created with basic information.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: "Recipe basic information saved successfully",
      data: {
        recipe_id: newRecipe.id,
        recipe_slug: newRecipe.recipe_slug,
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating recipe basic information"
    );
  }
};

/**
 * @description Handle ingredients, nutrition, and cuisine type data for a recipe
 * @route POST /api/v1/recipes/batch/ingredients-nutrition
 * @access Private
 */
const addIngredientsNutritionCuisine = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body) as RecipeIngredientsNutritionCuisineData;

    // Start transaction
    const transaction = await transactionManager.start();

    const {
      recipe_id,
      ingredients,
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
      haccp_attributes
    } = sanitizedBody;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const userRole = (req as any).user?.roles?.[0];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: "Unauthorized access",
      });
    }

    // Permission check: Only certain roles can update recipes
    if (!canCreateUpdateRecipes(userRole)) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate recipe exists
    const recipe = await Recipe.findOne({
      where: {
        id: recipe_id,
        organization_id: organizationId,
        recipe_status: {
          [Op.not]: RecipeStatus.deleted,
        },
      },
      transaction,
    });

    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Add ingredients if provided
    if (ingredients && ingredients.length > 0) {
      // First make any existing ingredients inactive
      await RecipeIngredients.update(
        { status: RecipeIngredientsStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      // Create new ingredient records
      const ingredientData = ingredients.map((ingredient) => ({
        recipe_id,
        ingredient_id: ingredient.id,
        ingredient_quantity: ingredient.quantity,
        ingredient_measure: ingredient.measure,
        ingredient_wastage: ingredient.wastage,
        ingredient_cost: ingredient.cost,
        cooking_method: ingredient.cooking_method,
        preparation_method: ingredient.preparation_method,
        status: RecipeIngredientsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await RecipeIngredients.bulkCreate(ingredientData, { transaction });

      // Update ingredient costs timestamp
      await updateRecipeCostTimestamp(recipe_id, transaction);
    }

    // Add nutrition attributes if provided
    if (
      nutrition_attributes && nutrition_attributes.length > 0 ||
      allergen_attributes ||
      cuisine_attributes ||
      dietary_attributes ||
      haccp_attributes
    ) {
      // First make any existing attributes inactive
      await RecipeAttributes.update(
        { status: RecipeAttributesStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      const attributesData = [];

      // Add nutrition attributes
      if (nutrition_attributes && nutrition_attributes.length > 0) {
        const nutritionData = nutrition_attributes.map((attr) => ({
          recipe_id,
          attribute_id: attr.id,
          unit_of_measure: attr.unit_of_measure,
          unit: attr.unit,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          attribute_type: "nutrition",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...nutritionData);
      }

      // Add allergen "contains" attributes
      if (allergen_attributes?.contains && allergen_attributes.contains.length > 0) {
        const allergenContains = allergen_attributes.contains.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "allergen_contains",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...allergenContains);
      }

      // Add allergen "may contain" attributes
      if (allergen_attributes?.may_contain && allergen_attributes.may_contain.length > 0) {
        const allergenMayContain = allergen_attributes.may_contain.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "allergen_may_contain",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...allergenMayContain);
      }

      // Add cuisine attributes
      if (cuisine_attributes && cuisine_attributes.length > 0) {
        const cuisineData = cuisine_attributes.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "cuisine",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...cuisineData);
      }

      // Add dietary attributes
      if (dietary_attributes && dietary_attributes.length > 0) {
        const dietaryData = dietary_attributes.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "dietary",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...dietaryData);
      }

      // Add HACCP attributes
      if (haccp_attributes && haccp_attributes.length > 0) {
        const haccpData = haccp_attributes.map((attr) => ({
          recipe_id,
          attribute_id: attr.id,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          attribute_type: "haccp",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...haccpData);
      }

      // Bulk create all attributes
      if (attributesData.length > 0) {
        await RecipeAttributes.bulkCreate(attributesData, { transaction });
      }

      // Update nutrition values timestamp
      await updateRecipeNutritionTimestamp(recipe_id, transaction);
    }

    // Update recipe
    await recipe.update(
      {
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Create recipe history entry
    await createRecipeHistory({
      recipe_id,
      action: RecipeHistoryAction.updated,
      description: `Recipe "${recipe.recipe_title}" ingredients, nutrition, and cuisine types were updated.`,
      ip_address: req.ip,
      user_agent: req.get("User-Agent") || "",
      organization_id: organizationId,
      created_by: userId,
    }, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe ingredients, nutrition, and cuisine data saved successfully",
      data: { recipe_id },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding ingredients and nutrition data"
    );
  }
};

/**
 * @description Handle recipe steps in batches
 * @route POST /api/v1/recipes/batch/steps
 * @access Private
 */
const addRecipeSteps = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    const {
      recipe_id,
      steps,
      batch_number,
      is_final_batch
    } = sanitizedBody;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const userRole = (req as any).user?.roles?.[0];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: "Unauthorized access",
      });
    }

    // Permission check: Only certain roles can update recipes
    if (!canCreateUpdateRecipes(userRole)) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate recipe exists
    const recipe = await Recipe.findOne({
      where: {
        id: recipe_id,
        organization_id: organizationId,
        recipe_status: {
          [Op.not]: RecipeStatus.deleted,
        },
      },
      transaction,
    });

    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Validate steps data
    if (!Array.isArray(steps)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Steps must be an array",
      });
    }

    // If this is the first batch, make all existing steps inactive
    if (batch_number === 1) {
      await RecipeSteps.update(
        { status: RecipeStepsStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );
    }

    // Calculate starting step number based on batch
    const startingStepNumber = ((batch_number || 1) - 1) * STEPS_BATCH_SIZE;

    // Create new step records with calculated step numbers
    if (steps && steps.length > 0) {
      const stepsData = steps.map((step: any, index: number) => ({
        recipe_id,
        step_number: startingStepNumber + index + 1, // 1-based step numbering
        step_title: step.step_title,
        step_description: step.step_description,
        step_note: step.step_note,
        step_warning: step.step_warning,
        step_time: step.step_time,
        step_temperature: step.step_temperature,
        step_temperature_unit: step.step_temperature_unit,
        status: RecipeStepsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await RecipeSteps.bulkCreate(stepsData, { transaction });
    }

    // Update recipe
    await recipe.update(
      {
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Only create history entry on final batch
    if (is_final_batch === true || is_final_batch === "true") {
      await createRecipeHistory({
        recipe_id,
        action: RecipeHistoryAction.updated,
        description: `Recipe "${recipe.recipe_title}" steps were updated.`,
        ip_address: req.ip,
        user_agent: req.get("User-Agent") || "",
        organization_id: organizationId,
        created_by: userId,
      }, transaction);
    }

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: `Recipe steps batch ${batch_number || 1} saved successfully`,
      data: {
        recipe_id,
        batch_number: batch_number || 1,
        steps_in_batch: steps.length,
        is_final_batch: is_final_batch === true || is_final_batch === "true"
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding recipe steps"
    );
  }
};

/**
 * @description Handle recipe file uploads in batches
 * @route POST /api/v1/recipes/batch/uploads
 * @access Private
 */
const addRecipeUploads = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();
  const fileTracker = new FileOperationTracker();

  try {
    // Start transaction
    const transaction = await transactionManager.start();

    // Parse form data
    const recipe_id = req.body.recipe_id;
    const batch_number = req.body.batch_number || 1;
    const is_final_batch = req.body.is_final_batch === "true" || req.body.is_final_batch === true;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const userRole = (req as any).user?.roles?.[0];

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: "Unauthorized access",
      });
    }

    // Permission check: Only certain roles can update recipes
    if (!canCreateUpdateRecipes(userRole)) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: "Permission denied",
      });
    }

    // Validate recipe exists
    const recipe = await Recipe.findOne({
      where: {
        id: recipe_id,
        organization_id: organizationId,
        recipe_status: {
          [Op.not]: RecipeStatus.deleted,
        },
      },
      transaction,
    });

    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // If this is the first batch, make all existing resources inactive
    if (batch_number === 1) {
      await RecipeResources.update(
        { status: RecipeResourceStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );
    }

    // Handle uploaded files
    const files = Array.isArray(req.files) ? req.files : [];

    if (files.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No files uploaded",
      });
    }

    // Upload each file and track it
    const uploadPromises = files.map(async (file) => {
      const uniqueFilename = `${Date.now()}-${file.originalname.replace(/\s+/g, "-")}`;
      const bucketName = process.env.NODE_ENV || "development";

      // Determine file path based on organization and recipe
      const orgName = organizationId ? organizationId.toString() : null;
      const filePath = orgName
        ? `${orgName}/recipe_files/${recipe_id}/${uniqueFilename}`
        : `recipe_defaults/recipe_files/${recipe_id}/${uniqueFilename}`;

      // Upload the file to S3 bucket
      const uploadResult = await uploadService.uploadFileToBucket(
        bucketName,
        filePath,
        file.buffer,
        file.mimetype
      );

      if (!uploadResult.success) {
        throw new Error(`Failed to upload file: ${file.originalname}`);
      }

      // Track file creation for potential rollback
      fileTracker.trackCreate(filePath);

      return {
        originalName: file.originalname,
        fileName: uniqueFilename,
        fileSize: file.size,
        mimeType: file.mimetype,
        filePath: filePath,
        bucketName: bucketName
      };
    });

    const uploadedFiles = await Promise.all(uploadPromises);

    // Create resource records for each uploaded file
    const resourcesData = uploadedFiles.map((file, index) => ({
      recipe_id,
      resource_title: file.originalName,
      resource_type: file.mimeType.startsWith("image/") ? "image" : "document",
      resource_key: file.filePath,
      resource_url: `${global.config.API_BASE_URL}/${file.filePath}`,
      resource_size: file.fileSize,
      resource_order: ((batch_number - 1) * UPLOADS_BATCH_SIZE) + index + 1,
      status: RecipeResourceStatus.active,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));

    await RecipeResources.bulkCreate(resourcesData, { transaction });

    // Update recipe
    await recipe.update(
      {
        updated_by: userId,
        updated_at: new Date()
      },
      { transaction }
    );

    // Only create history entry on final batch
    if (is_final_batch) {
      await createRecipeHistory({
        recipe_id,
        action: RecipeHistoryAction.updated,
        description: `Recipe "${recipe.recipe_title}" resources were updated.`,
        ip_address: req.ip,
        user_agent: req.get("User-Agent") || "",
        organization_id: organizationId,
        created_by: userId,
      }, transaction);
    }

    // Commit transaction
    await transactionManager.commit();
    fileTracker.clear();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: `Recipe uploads batch ${batch_number} saved successfully`,
      data: {
        recipe_id,
        batch_number,
        files_uploaded: uploadedFiles.length,
        is_final_batch
      },
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    await fileTracker.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error uploading recipe files"
    );
  }
};

export default {
  createRecipeBasicInfo,
  addIngredientsNutritionCuisine,
  addRecipeSteps,
  addRecipeUploads
};
