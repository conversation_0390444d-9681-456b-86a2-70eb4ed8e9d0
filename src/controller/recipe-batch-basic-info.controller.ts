import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { generateUniqueSlug } from "../helper/slugGenerator";
import { createRecipeHistory } from "../helper/recipe.helper";
import {
  TransactionManager,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import {
  checkBatchAuthorization,
  validateRecipeTitle,
  createCreatedResponse,
  createHistoryData,
} from "../helper/recipe-batch-common.helper";

// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;
const RecipeAttributes = db.RecipeAttributes;

/**
 * @description Handle the basic information step of recipe creation
 * @route POST /api/v1/recipes/batch/basic-info
 * @access Private
 */
const createRecipeBasicInfo = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();

    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_serve_in,
      recipe_complexity_level,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_placeholder,
      categories,
      dietary_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual
    } = sanitizedBody;

    // Common authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (!authResult) return;
    const { userId, organizationId } = authResult;

    // Validate required fields
    if (!validateRecipeTitle(recipe_title, res)) return;

    // Generate unique slug from recipe title
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existingRecipe = await Recipe.findOne({
        where: {
          recipe_slug: slug,
          organization_id: organizationId,
          recipe_status: {
            [Op.not]: RecipeStatus.deleted,
          },
        },
      });
      return !!existingRecipe;
    };

    // Generate unique slug
    const recipe_slug = await generateUniqueSlug(
      recipe_title,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Create main recipe with initial timestamps
    const currentTimestamp = new Date();
    const recipeData = {
      // Basic info
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility: has_recipe_public_visibility || false,
      has_recipe_private_visibility: has_recipe_private_visibility || false,
      recipe_status: recipe_status || RecipeStatus.draft,
      recipe_complexity_level,

      // Serving details
      recipe_serve_in,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,

      recipe_placeholder,
      recipe_slug,
      ingredient_costs_updated_at: currentTimestamp,
      nutrition_values_updated_at: currentTimestamp,
      is_ingredient_cooking_method:
        is_ingredient_cooking_method === "true" ||
        is_ingredient_cooking_method === true,
      is_preparation_method:
        is_preparation_method === "true" || is_preparation_method === true,
      is_cost_manual:
        is_cost_manual === "true" || is_cost_manual === true,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    };

    const newRecipe = await Recipe.create(recipeData, { transaction });

    // Add categories if provided
    if (categories && Array.isArray(categories) && categories.length > 0) {
      const categoryData = categories.map((categoryId: number) => ({
        recipe_id: newRecipe.id,
        category_id: categoryId,
        status: "active",
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await db.RecipeCategory.bulkCreate(categoryData, { transaction });
    }

    // Create recipe attributes (optional for draft) - Type-wise handling
    const allAttributeData: any[] = [];

    // Handle dietary attributes (simple IDs)
    if (dietary_attributes && dietary_attributes.length > 0) {
      const dietaryData = dietary_attributes.map((attrId: number) => ({
        recipe_id: newRecipe.id,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...dietaryData);
    }
    
    // Bulk create all attributes if any exist
    if (allAttributeData.length > 0) {
      await RecipeAttributes.bulkCreate(allAttributeData, { transaction });
    }

    // Create recipe history entry for basic info
    const historyData = createHistoryData(
      newRecipe.id,
      RecipeHistoryAction.created,
      `Recipe "${recipe_title}" was created with basic information.`,
      req,
      organizationId,
      userId
    );
    await createRecipeHistory(historyData, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.CREATED).json(
      createCreatedResponse("Recipe basic information saved successfully", {
        recipe_id: newRecipe.id,
        recipe_slug: newRecipe.recipe_slug,
      })
    );
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating recipe basic information"
    );
  }
};

export default {
  createRecipeBasicInfo,
};
