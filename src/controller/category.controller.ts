import { Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { sequelize, db } from "../models";
import { CategoryStatus } from "../models/Category";
import { RecipeCategoryStatus } from "../models/RecipeCategory";
import { IngredientCategoryStatus } from "../models/IngredientCategory";
import { generateUniqueSlug } from "../helper/slugGenerator";
import {
  RECIPE_FILE_UPLOAD_CONSTANT,
  isDefaultAccess,
  getPaginatedItems,
  getUserFullName,
} from "../helper/common";
import uploadService from "../helper/upload.service";
import {
  TransactionManager,
  FileOperationTracker,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";

// Get models from db object to ensure associations are set up
const Category = db.Category;
const Item = db.Item;
const RecipeCategory = db.RecipeCategory;
const IngredientCategory = db.IngredientCategory;

// Helper function to get the proper base URL
const getBaseUrl = (): string => {
  const baseUrl = global.config?.API_BASE_URL;

  if (
    baseUrl &&
    baseUrl.includes("/backend-api/v1/public/user/get-file?location=")
  ) {
    // API_BASE_URL already contains the full endpoint, return base part
    return baseUrl.replace(
      "/backend-api/v1/public/user/get-file?location=",
      ""
    );
  } else {
    // For development or when API_BASE_URL is just the base domain
    return (
      process.env.BASE_URL ||
      process.env.FRONTEND_URL ||
      "https://staging.namastevillage.theeasyaccess.com"
    );
  }
};

/**
 * Create a new category
 * @route POST /api/v1/private/category
 * @access Private (Authenticated users)
 */
const createCategory = async (req: any, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();
  const fileTracker = new FileOperationTracker();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const {
      category_name,
      category_description,
      category_status,
      category_type,
    } = sanitizedBody;

    // Start transaction
    const transaction = await transactionManager.start();

    const trimmed_category_name = category_name.trim();

    if (trimmed_category_name.length < 2) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CATEGORY_NAME_TOO_SHORT"),
      });
    }

    // Check if user has default access (can create system defaults)
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Determine organization ID for creation
    const organizationIdForCreation = hasDefaultAccess
      ? null
      : req.user?.organization_id;

    // 🔍 ENHANCED UNIQUENESS VALIDATION
    // Check for existing category with same name (case-insensitive)
    // Ensures uniqueness within organization scope AND prevents conflicts with system defaults
    const existingCategoryByName = await Category.findOne({
      where: {
        [Op.and]: [
          db.sequelize.where(
            db.sequelize.fn("LOWER", db.sequelize.col("category_name")),
            db.sequelize.fn("LOWER", trimmed_category_name)
          ),
          {
            category_type: category_type, // Must be same type
          },
          {
            [Op.or]: [
              { organization_id: organizationIdForCreation }, // Same organization
              { organization_id: null }, // Default/system records
            ],
          },
        ],
      },
    });

    if (existingCategoryByName) {
      const conflictType = existingCategoryByName.organization_id
        ? "organization"
        : "system default";

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: `Category name "${trimmed_category_name}" already exists in ${conflictType} ${category_type} categories`,
        error_code: "CATEGORY_NAME_DUPLICATE",
        conflict_details: {
          existing_category_id: existingCategoryByName.id,
          conflict_type: conflictType,
          category_type: category_type,
        },
      });
    }

    // 🔍 SLUG UNIQUENESS VALIDATION
    let categorySlug = trimmed_category_name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-+|-+$/g, "");

    const existingCategoryBySlug = await Category.findOne({
      where: {
        [Op.and]: [
          { category_slug: categorySlug },
          { category_type: category_type },
          {
            [Op.or]: [
              { organization_id: organizationIdForCreation },
              { organization_id: null },
            ],
          },
        ],
      },
    });

    if (existingCategoryBySlug) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: `Category slug "${categorySlug}" already exists`,
        error_code: "CATEGORY_SLUG_DUPLICATE",
      });
    }

    // Generate unique slug from category name with transaction lock
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existing = await Category.findOne({
        where: {
          category_slug: slug,
          organization_id: organizationIdForCreation,
        },
        transaction, // Use transaction to prevent race conditions
        lock: true, // Add row-level lock
      });
      return !!existing;
    };

    categorySlug = await generateUniqueSlug(
      trimmed_category_name,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Set system category flag based on default access
    const isSystemCategory = hasDefaultAccess;

    // Create category first
    const categoryAttributes = {
      category_name: trimmed_category_name,
      category_slug: categorySlug,
      category_description: category_description,
      category_status: category_status || CategoryStatus.active,
      organization_id: organizationIdForCreation,
      category_type: category_type,
      is_system_category: isSystemCategory,
      created_by: req.user?.id || null,
      updated_by: req.user?.id || null,
    };

    const category = await Category.create(categoryAttributes, { transaction });

    let iconItemId = null;
    let uploadedFile = null;

    // Handle file upload from multerS3 middleware (req.files format)
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      if (files.categoryIcon && files.categoryIcon.length > 0) {
        uploadedFile = files.categoryIcon[0];
        iconItemId = uploadedFile.item_id;
      }
    }

    // Handle file path update after category creation
    if (uploadedFile && uploadedFile.isMovable) {
      try {
        // Get organization name for path generation
        // For system defaults (null org_id), pass null; otherwise convert to string
        const orgName = organizationIdForCreation
          ? organizationIdForCreation.toString()
          : null;

        // Generate the correct destination path
        const destinationPath =
          RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(
            orgName,
            category.id,
            uploadedFile.filename
          );

        // Track file operation for potential rollback
        fileTracker.trackMove(uploadedFile.path, destinationPath);

        // Move file to correct location
        const bucketName = process.env.NODE_ENV || "development";
        const moveResult = await uploadService.moveFileInBucket(
          bucketName,
          uploadedFile.path,
          destinationPath,
          uploadedFile.item_id
        );

        if (!moveResult.success) {
          throw new Error(`Failed to move category icon: ${moveResult.error}`);
        }
      } catch (error) {
        console.error("Error moving uploaded file:", error);
      }
    }

    // Update category with icon item_id if uploaded
    if (iconItemId) {
      await category.update({ category_icon: iconItemId }, { transaction });
    }

    // Commit transaction
    await transactionManager.commit();

    // Clear file operations after successful commit
    fileTracker.clear();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("CATEGORY_CREATED_SUCCESSFULLY"),
    });
  } catch (error) {
    await transactionManager.rollback();
    await fileTracker.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating category"
    );
  }
};

/**
 * Get all categories with advanced filtering and search
 * @route GET /api/v1/private/category
 * @access Private (Authenticated users)
 *
 * Query Parameters:
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 10)
 * - search: Search in name and description
 * - status: Filter by category status (active, inactive)
 * - type: Filter by category type (recipe, ingredient)
 * - sort: Sort field (default: category_name)
 * - order: Sort order (ASC/DESC, default: ASC)
 */
const getAllCategories = async (req: any, res: Response): Promise<any> => {
  try {
    const {
      page,
      limit,
      search = "",
      status,
      type,
      categoryUse,
      isSystemCategory,
      organizationId,
      sort = "category_name",
      order = "ASC",
    } = req.query;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build base where clause with organization isolation
    let whereClause: any = {};
    const searchConditions: any[] = [];

    // Organization-based access control
    if (hasDefaultAccess) {
      // Admin users can see all records, but can filter by organization
      if (organizationId !== undefined) {
        if (organizationId === "null" || organizationId === "") {
          whereClause.organization_id = null; // System defaults
        } else {
          whereClause.organization_id = organizationId;
        }
      }
    } else {
      // Regular users see their org records + system defaults
      if (req.user.organization_id) {
        whereClause = {
          [Op.or]: [
            { organization_id: req.user?.organization_id },
            { organization_id: null }, // System defaults
            { is_system_category: true }, // System categories
          ],
        };
      }
    }

    // Filter by category status (active, inactive)
    if (status) {
      whereClause.category_status = status;
    }

    // Filter by category type (recipe, ingredient)
    if (type) {
      whereClause.category_type = type;
    }

    // Filter by categoryUse (alias for type parameter)
    if (categoryUse) {
      whereClause.category_type = categoryUse;
    }

    // Filter by system category flag - handle both true and false values correctly
    if (isSystemCategory !== undefined) {
      if (isSystemCategory === "true") {
        whereClause.is_system_category = true;
      } else if (isSystemCategory === "false") {
        whereClause.is_system_category = false;
      }
    }

    // Search functionality (searches in name and description)
    if (search) {
      searchConditions.push({
        [Op.or]: [
          { category_name: { [Op.like]: `%${search}%` } },
          { category_description: { [Op.like]: `%${search}%` } },
        ],
      });
    }

    // Combine search conditions with where clause
    if (searchConditions.length > 0) {
      if (Object.keys(whereClause).length > 0) {
        whereClause = {
          [Op.and]: [whereClause, ...searchConditions],
        };
      } else {
        whereClause = {
          [Op.and]: searchConditions,
        };
      }
    }

    // Handle pagination - if limit is not provided, show all records
    const pageNumber = page ? Number(page) : 1;
    const limitNumber = limit ? Number(limit) : null; // null means no limit
    const offset = limitNumber ? (pageNumber - 1) * limitNumber : 0;

    // Validate sort field to prevent SQL injection
    const allowedSortFields = [
      "category_name",
      "category_description",
      "category_status",
      "category_type",
      "is_system_category",
      "organization_id",
      "created_at",
      "updated_at",
      "created_by",
      "updated_by",
    ];

    const sortField = allowedSortFields.includes(sort as string)
      ? (sort as string)
      : "category_name";
    const sortOrder =
      (order as string).toUpperCase() === "DESC" ? "DESC" : "ASC";

    // Build query options - conditionally add limit and offset
    const queryOptions: any = {
      where: whereClause,
      include: [
        {
          model: Item,
          as: "iconItem",
          attributes: [
            "id",
            "item_location",
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
              "iconUrl",
            ],
            // Add computed hasIcon field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
              "hasIcon",
            ],
          ],
          required: false, // LEFT JOIN to include categories without icons
        },
      ],
      order: [[sortField, sortOrder]],
      // Ensure we get raw data with computed fields
      raw: false,
      nest: true,
    };

    // Only add limit and offset if limit is provided
    if (limitNumber) {
      queryOptions.limit = limitNumber;
      queryOptions.offset = offset;
    }

    // Fetch categories with conditional pagination
    const { rows: categories, count } =
      await Category.findAndCountAll(queryOptions);

    // Optimize user name fetching to prevent N+1 queries
    const userIds = new Set<number>();
    categories.forEach((category: any) => {
      if (category.created_by) userIds.add(category.created_by);
      if (category.updated_by) userIds.add(category.updated_by);
    });

    // Fetch all user names in a single batch
    const userNamesMap = new Map<number, string>();
    if (userIds.size > 0) {
      const userNamesPromises = Array.from(userIds).map(async (userId) => {
        const userName = await getUserFullName(userId);
        return { userId, userName };
      });

      const userNamesResults = await Promise.all(userNamesPromises);
      userNamesResults.forEach(({ userId, userName }) => {
        userNamesMap.set(userId, userName);
      });
    }

    // Add user names to the categories
    const categoriesWithUserNames = categories.map((category: any) => {
      const categoryData = category.toJSON ? category.toJSON() : category;

      // Add created_by and updated_by user names from cache
      if (categoryData.created_by) {
        categoryData.created_by_name =
          userNamesMap.get(categoryData.created_by) || "Unknown User";
      }
      if (categoryData.updated_by) {
        categoryData.updated_by_name =
          userNamesMap.get(categoryData.updated_by) || "Unknown User";
      }

      return categoryData;
    });

    // Calculate pagination info only if limit is provided
    let paginationInfo: any = {
      count: count,
      data: categoriesWithUserNames,
    };

    if (limitNumber) {
      const { total_pages } = getPaginatedItems(
        limitNumber,
        pageNumber,
        count || 0
      );

      paginationInfo = {
        ...paginationInfo,
        page: pageNumber,
        limit: limitNumber,
        total_pages: total_pages,
      };
    } else {
      // When no limit is provided, show all records info
      paginationInfo = {
        ...paginationInfo,
        page: 1,
        limit: "all",
        total_pages: 1,
        showing_all_records: true,
      };
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      ...paginationInfo,
    });
  } catch (error) {
    console.log("Error in category.controller.ts - getAllCategories:", error);
    console.error("Error fetching categories:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Get single category by ID
 * @route GET /api/v1/private/category/:id
 * @access Private (Authenticated users)
 */
const getCategoryById = async (req: any, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only see their org records + system defaults
      whereClause = {
        id,
        [Op.or]: [
          { organization_id: req.user?.organization_id },
          { organization_id: null }, // System defaults
          { is_system_category: true }, // System categories
        ],
      };
    }

    const category = await Category.findOne({
      where: whereClause,
      include: [
        {
          model: Item,
          as: "iconItem",
          attributes: [
            "id",
            "item_name",
            "item_location",
            "item_mime_type",
            // Add computed iconUrl field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN CONCAT('${getBaseUrl()}/backend-api/v1/public/user/get-file?location=', iconItem.item_location)
                ELSE NULL
              END`),
              "iconUrl",
            ],
            // Add computed hasIcon field at database level
            [
              sequelize.literal(`CASE
                WHEN iconItem.item_location IS NOT NULL
                THEN true
                ELSE false
              END`),
              "hasIcon",
            ],
          ],
          required: false, // LEFT JOIN to include category even without icon
        },
      ],
      raw: false,
      nest: true,
    });

    if (!category) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("CATEGORY_NOT_FOUND"),
        data: {},
      });
    }

    // Add user names to the category
    const categoryData = category.toJSON ? category.toJSON() : category;

    // Add created_by and updated_by user names
    if (categoryData.created_by) {
      categoryData.created_by_name = await getUserFullName(
        categoryData.created_by
      );
    }
    if (categoryData.updated_by) {
      categoryData.updated_by_name = await getUserFullName(
        categoryData.updated_by
      );
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: categoryData,
    });
  } catch (error) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: process.env.NODE_ENV === "development" ? error : undefined,
    });
  }
};

/**
 * Update category by ID
 * @route PUT /api/v1/private/category/:id
 * @access Private (Authenticated users)
 */
const updateCategory = async (req: any, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();
  const fileTracker = new FileOperationTracker();

  try {
    const { id } = req.params;

    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const {
      category_name,
      category_description,
      category_status,
      category_type,
      is_system_category,
    } = sanitizedBody;

    // Start transaction
    const transaction = await transactionManager.start();

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only update their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can update any record
      whereClause = { id };
    }

    // Find the category
    const category = await Category.findOne({
      where: whereClause,
    });

    if (!category) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CATEGORY_NOT_FOUND"),
      });
    }

    // Prevent regular users from updating system default records
    if (category.organization_id === null && !hasDefaultAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("CANNOT_UPDATE_SYSTEM_DEFAULT"),
      });
    }

    // 🔍 BUSINESS VALIDATION: Check for duplicate category name (excluding current category)
    if (category_name && category_name.trim() !== category.category_name) {
      // Check both in same organization AND in default/system records
      const existingCategoryByName = await Category.findOne({
        where: {
          [Op.and]: [
            db.sequelize.where(
              db.sequelize.fn("LOWER", db.sequelize.col("category_name")),
              db.sequelize.fn("LOWER", category_name.trim())
            ),
            {
              [Op.or]: [
                { organization_id: req.user?.organization_id }, // Same organization
                { organization_id: null }, // Default/system records
              ],
            },
            { id: { [Op.ne]: id } }, // Exclude current category
          ],
        },
      });

      if (existingCategoryByName) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("CATEGORY_NAME_ALREADY_EXISTS"),
        });
      }
    }

    // Generate new slug if category name is being updated
    let newSlug = category.category_slug;
    if (category_name && category_name !== category.category_name) {
      const checkSlugExists = async (slug: string): Promise<boolean> => {
        // For system defaults, check globally; for org records, check within org
        const slugCheckOrgId =
          hasDefaultAccess && category.organization_id === null
            ? null
            : req.user?.organization_id;

        const existing = await Category.findOne({
          where: {
            category_slug: slug,
            organization_id: slugCheckOrgId,
            id: { [Op.ne]: id },
          },
          transaction, // Use transaction to prevent race conditions
          lock: true, // Add row-level lock
        });
        return !!existing;
      };

      newSlug = await generateUniqueSlug(category_name, checkSlugExists, {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      });
    }

    let newIconItemId = category.category_icon;
    let iconWasUpdated = false;

    // Handle file upload from multerS3 middleware (req.files format)
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };
      if (files.categoryIcon && files.categoryIcon.length > 0) {
        const uploadedFile = files.categoryIcon[0];

        // Handle file movement if needed
        if (uploadedFile.isMovable) {
          const orgName = category.organization_id
            ? category.organization_id.toString()
            : null;
          const destinationPath =
            RECIPE_FILE_UPLOAD_CONSTANT.CATEGORY_ICON.destinationPath(
              orgName,
              category.id,
              uploadedFile.filename
            );

          // Track file operation for potential rollback
          fileTracker.trackMove(uploadedFile.path, destinationPath);

          const bucketName = process.env.NODE_ENV || "development";
          const moveResult = await uploadService.moveFileInBucket(
            bucketName,
            uploadedFile.path,
            destinationPath,
            uploadedFile.item_id
          );

          if (!moveResult.success) {
            throw new Error(
              `Failed to move category icon: ${moveResult.error}`
            );
          }
        }

        newIconItemId = uploadedFile.item_id;
        iconWasUpdated = true;
      } else if (files.categoryIcon && files.categoryIcon.length === 0) {
        // Empty file array means user wants to remove the icon
        newIconItemId = null;
        iconWasUpdated = true;
      }
    } else if (req.body.categoryIcon === "" || req.body.categoryIcon === null) {
      // Handle explicit removal of icon via form data
      newIconItemId = null;
      iconWasUpdated = true;
    }

    // Update the category
    await Category.update(
      {
        category_name: category_name || category.category_name,
        category_slug: newSlug,
        category_description:
          category_description !== undefined
            ? category_description
            : category.category_description,
        category_icon: newIconItemId,
        category_status: category_status || category.category_status,
        category_type: category_type || category.category_type,
        is_system_category:
          is_system_category !== undefined
            ? is_system_category
            : category.is_system_category,
        updated_by: req.user?.id || null,
      },
      {
        where: whereClause,
        transaction,
      }
    );

    // Fetch updated category using the same where clause
    const updatedCategory = await Category.findOne({
      where: whereClause,
      transaction,
    });

    // Commit transaction
    await transactionManager.commit();

    // Clear file operations after successful commit
    fileTracker.clear();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CATEGORY_UPDATED_SUCCESSFULLY"),
    });
  } catch (error) {
    await transactionManager.rollback();
    await fileTracker.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error updating category"
    );
  }
};

/**
 * Delete category by ID (hard delete if not in use, otherwise show usage message)
 * @route DELETE /api/v1/private/category/:id
 * @access Private (Authenticated users)
 */
const deleteCategory = async (req: any, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const transaction = await transactionManager.start();
    const { id } = req.params;

    // Check if user has default access
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Build where clause based on user access
    let whereClause: any = { id };

    if (!hasDefaultAccess) {
      // Regular users can only delete their org records
      whereClause = {
        id,
        organization_id: req.user?.organization_id,
      };
    } else {
      // Super admin can delete any record
      whereClause = { id };
    }

    const category = await Category.findOne({
      where: whereClause,
      transaction,
    });

    if (!category) {
      await transactionManager.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("CATEGORY_NOT_FOUND"),
      });
    }

    // Prevent deletion of system default records (organization_id: null) by non-default users
    if (category.organization_id === null && !hasDefaultAccess) {
      await transactionManager.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_DELETE_SYSTEM_DEFAULT"),
      });
    }

    // Prevent deletion of system categories by non-default users
    if (category.is_system_category && !hasDefaultAccess) {
      await transactionManager.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CANNOT_DELETE_SYSTEM_CATEGORY"),
      });
    }

    // Check if category is being used in recipes (any status)
    const recipeUsage = await RecipeCategory.count({
      where: {
        category_id: id,
      },
      transaction,
    });

    // Check if category is being used in ingredients (any status)
    const ingredientUsage = await IngredientCategory.count({
      where: {
        category_id: id,
      },
      transaction,
    });

    const totalUsage = recipeUsage + ingredientUsage;

    // If category is in use, prevent deletion
    if (totalUsage > 0) {
      await transactionManager.rollback();

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("CATEGORY_IN_USE_CANNOT_DELETE"),
      });
    }

    // Hard delete the category and all its inactive relations
    await IngredientCategory.destroy({
      where: { category_id: id },
      transaction,
    });

    await RecipeCategory.destroy({
      where: { category_id: id },
      transaction,
    });

    await Category.destroy({
      where: whereClause,
      transaction,
    });

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("CATEGORY_DELETED_SUCCESSFULLY"),
    });
  } catch (error) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting category"
    );
  }
};

// Default export object
export default {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
};
