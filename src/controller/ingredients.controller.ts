import { Request, Response } from "express";
import { IngredientStatus } from "../models/Ingreditant";
import { sequelize, db } from "../models";
import { IngredientAttributesStatus } from "../models/IngredientAttributes";
import { IngredientCategoryStatus } from "../models/IngredientCategory";
import { IngredientConversionStatus } from "../models/IngredientConversion";
import { generateSlug, generateUniqueSlug } from "../helper/slugGenerator";
import { Op } from "sequelize";
import { StatusCodes } from "http-status-codes";
import * as ExcelJS from "exceljs";
import {
  getPaginatedItems,
  getPagination,
  getUserFullName,
  getOrgName,
} from "../helper/common";
import { updateIngredientNutritionTimestamp } from "../helper/timestamp.helper";
import {
  TransactionManager,
  FileOperationTracker,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import { AttributeStatus } from "../models/FoodAttributes";
import { CategoryStatus } from "../models/Category";
import { MeasureStatus } from "../models/RecipeMeasure";

// Get models from db object to ensure associations are set up
const Ingredient = db.Ingredient;
const IngredientAttributes = db.IngredientAttributes;
const IngredientCategory = db.IngredientCategory;
const IngredientConversion = db.IngredientConversion;
const RecipeIngredients = db.RecipeIngredients;
const FoodAttributes = db.FoodAttributes;
const Category = db.Category;
const RecipeMeasure = db.RecipeMeasure;

// Add interface for error handling
interface CustomError {
  message: string;
}

/**
 * @description Create a new ingredient with its relations
 * @route POST /api/v1/ingredients
 * @access Private
 */
const createIngredient = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    const {
      ingredient_name,
      ingredient_description,
      ingredient_status,
      waste_percentage,
      unit_of_measure,
      cost_per_unit,
      categories,
      nutrition_attributes,
      allergy_attributes,
      dietary_attributes,
      conversions,
    } = sanitizedBody;

    // Start transaction
    const transaction = await transactionManager.start();

    // Validate required fields
    if (!ingredient_name || !cost_per_unit || !unit_of_measure) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("MISSING_REQUIRED_FIELDS"),
        required_fields: [
          "ingredient_name",
          "cost_per_unit",
          "unit_of_measure",
        ],
        provided_fields: Object.keys(req.body),
      });
    }

    // Handle organization ID based on user role
    const { isDefaultAccess } = await import("../helper/common");
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Determine organization ID for creation
    const organizationIdForCreation = hasDefaultAccess
      ? null // Admin users create system defaults
      : req.user?.organization_id;

    // Generate unique slug from ingredient name with transaction lock
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existing = await Ingredient.findOne({
        where: {
          ingredient_slug: slug,
          organization_id: organizationIdForCreation,
        },
        transaction, // Use transaction to prevent race conditions
        lock: true, // Add row-level lock
      });
      return !!existing;
    };

    // Generate slug from ingredient name
    const ingredient_slug = await generateUniqueSlug(
      ingredient_name,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Validate and set ingredient status
    let validatedStatus = IngredientStatus.active; // Default to active
    if (ingredient_status) {
      if (ingredient_status === "active") {
        validatedStatus = IngredientStatus.active;
      } else if (ingredient_status === "inactive") {
        validatedStatus = IngredientStatus.inactive;
      }
    }

    // Create ingredient with initial timestamps
    const currentTimestamp = new Date();
    const ingredient = await Ingredient.create(
      {
        ingredient_name,
        ingredient_slug,
        ingredient_description,
        waste_percentage,
        unit_of_measure,
        cost_per_unit,
        cost_last_updated_at: currentTimestamp,
        nutrition_last_updated_at: currentTimestamp,
        organization_id: organizationIdForCreation,
        ingredient_status: validatedStatus,
        created_by: req.user.id,
        updated_by: req.user.id,
      },
      { transaction }
    );

    // Add categories if provided
    if (categories && categories.length > 0) {
      const categoryMaps = categories.map((categoryId: number) => ({
        category_id: categoryId,
        ingredient_id: ingredient.id,
        ingredient_category_status: IngredientCategoryStatus.active,
        organization_id: organizationIdForCreation,
        created_by: req.user.id,
        updated_by: req.user.id,
      }));
      await IngredientCategory.bulkCreate(categoryMaps, { transaction });
    }

    // Collect all attributes for single bulkCreate
    const allAttributeMaps: any[] = [];

    // Add nutrition attributes if provided (with unit & unit_of_measure)
    if (nutrition_attributes && nutrition_attributes.length > 0) {
      const nutritionMaps = nutrition_attributes.map((attr: any) => ({
        ingredient_id: ingredient.id,
        attributes_id: attr.attribute_id,
        unit_of_measure: attr.unit_of_measure,
        unit: attr.unit,
        ingredient_attributes_status: IngredientAttributesStatus.active,
        organization_id: organizationIdForCreation,
        created_by: req.user.id,
        updated_by: req.user.id,
      }));
      allAttributeMaps.push(...nutritionMaps);
    }

    // Add allergy attributes if provided (just IDs)
    if (allergy_attributes && allergy_attributes.length > 0) {
      const allergyMaps = allergy_attributes.map((attributeId: number) => ({
        ingredient_id: ingredient.id,
        attributes_id: attributeId,
        ingredient_attributes_status: IngredientAttributesStatus.active,
        organization_id: organizationIdForCreation,
        created_by: req.user.id,
        updated_by: req.user.id,
      }));
      allAttributeMaps.push(...allergyMaps);
    }

    // Add dietary attributes if provided (just IDs)
    if (dietary_attributes && dietary_attributes.length > 0) {
      const dietaryMaps = dietary_attributes.map((attributeId: number) => ({
        ingredient_id: ingredient.id,
        attributes_id: attributeId,
        ingredient_attributes_status: IngredientAttributesStatus.active,
        organization_id: organizationIdForCreation,
        created_by: req.user.id,
        updated_by: req.user.id,
      }));
      allAttributeMaps.push(...dietaryMaps);
    }

    // Single bulkCreate for all attributes
    if (allAttributeMaps.length > 0) {
      await IngredientAttributes.bulkCreate(allAttributeMaps, { transaction });
    }

    // Add unit conversions if provided
    if (conversions && conversions.length > 0) {
      const conversionMaps = conversions.map((conv: any) => ({
        ingredient_id: ingredient.id,
        from_measure: conv.from_measure,
        from_measure_value: conv.from_measure_value,
        to_measure: conv.to_measure,
        to_measure_value: conv.to_measure_value,
        ingredient_conversion_status: IngredientConversionStatus.active,
        organization_id: organizationIdForCreation,
        created_by: req.user.id,
        updated_by: req.user.id,
      }));
      await IngredientConversion.bulkCreate(conversionMaps, { transaction });
    }

    await transactionManager.commit();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("INGREDIENT_CREATED_SUCCESSFULLY"),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating ingredient"
    );
  }
};

/**
 * @description Handle export functionality for ingredients
 * @param queryParams - Request query parameters
 * @param ingredients - Fetched ingredients data
 * @param count - Total count of ingredients
 * @param downloadType - 'excel' or 'csv'
 * @param res - Response object
 */
const handleExport = async (
  queryParams: any,
  ingredients: any[],
  count: number,
  downloadType: string,
  res: Response,
  organizationId?: string
): Promise<any> => {
  try {
    const {
      ingredient_status,
      category,
      allergy,
      cuisine,
      dietary,
      search,
      sort_by,
      sort_order,
      page,
      limit,
    } = queryParams;

    // Build filter information
    const appliedFilters: string[] = [];
    if (ingredient_status) appliedFilters.push(`Status: ${ingredient_status}`);
    if (category) appliedFilters.push(`Category: ${category}`);
    if (allergy) appliedFilters.push(`Allergy: ${allergy}`);
    if (cuisine) appliedFilters.push(`Cuisine: ${cuisine}`);
    if (dietary) appliedFilters.push(`Dietary: ${dietary}`);
    if (search) appliedFilters.push(`Search: "${search}"`);
    if (sort_by)
      appliedFilters.push(`Sort: ${sort_by} (${sort_order || "ASC"})`);
    if (limit) appliedFilters.push(`Page: ${page || 1}, Limit: ${limit}`);

    const filterInfo =
      appliedFilters.length > 0 ? appliedFilters.join(", ") : "None";

    if (downloadType === "excel") {
      return await exportToExcel(
        ingredients,
        count,
        filterInfo,
        res,
        organizationId
      );
    } else if (downloadType === "csv") {
      return await exportToCSV(ingredients, count, filterInfo, res);
    }
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_EXPORTING_INGREDIENTS"),
      error: customError.message,
    });
  }
};

/**
 * @description Export ingredients to Excel format
 */
const exportToExcel = async (
  ingredients: any[],
  count: number,
  filterInfo: string,
  res: Response,
  organizationId?: string
): Promise<any> => {
  // Check for large datasets and warn user
  if (count > 10000) {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message:
        "Dataset too large for export. Please apply filters to reduce the number of records below 10,000.",
      current_count: count,
      max_allowed: 10000,
    });
  }
  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Ingredients Export");

  // Get organization name
  const orgName = organizationId
    ? await getOrgName(organizationId)
    : "Organization";

  // Add organization name at the top with styling
  const orgRow = worksheet.addRow([orgName || "Organization"]);
  orgRow.font = { bold: true, size: 16, color: { argb: "FF135e96" } };
  orgRow.alignment = { horizontal: "center" };
  worksheet.mergeCells(1, 1, 1, 15); // Merge across all columns

  // Add empty row for spacing
  worksheet.addRow([]);

  // Add export information with better formatting
  const filterRow = worksheet.addRow([`Export Filters Applied: ${filterInfo}`]);
  filterRow.font = { bold: true, size: 12 };

  const dateRow = worksheet.addRow([
    `Export Date: ${new Date().toLocaleString()}`,
  ]);
  dateRow.font = { bold: true, size: 12 };

  const countRow = worksheet.addRow([`Total Records: ${count}`]);
  countRow.font = { bold: true, size: 12 };

  worksheet.addRow([]); // Empty row

  // Add headers with improved styling
  const headers = [
    "ID",
    "Name",
    "Description",
    "Status",
    "Cost",
    "Unit of Measure",
    "Categories",
    "Allergens",
    "Dietary Info",
    "Cuisine",
    "Waste %",
    "Created By",
    "Updated By",
    "Created At",
    "Updated At",
  ];

  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFFFF" }, size: 12 }; // White text
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FF135e96" }, // Your specified color
  };
  headerRow.alignment = { horizontal: "center", vertical: "middle" };
  headerRow.height = 25; // Set row height

  // Optimize user name fetching to prevent N+1 queries
  const userIds = new Set<number>();
  ingredients.forEach((ingredient: any) => {
    const ingredientData = ingredient.toJSON ? ingredient.toJSON() : ingredient;
    if (ingredientData.created_by) userIds.add(ingredientData.created_by);
    if (ingredientData.updated_by) userIds.add(ingredientData.updated_by);
  });

  // Fetch all user names in a single batch
  const userNamesMap = new Map<number, string>();
  if (userIds.size > 0) {
    const userNamesPromises = Array.from(userIds).map(async (userId) => {
      const userName = await getUserFullName(userId);
      return { userId, userName };
    });

    const userNamesResults = await Promise.all(userNamesPromises);
    userNamesResults.forEach(({ userId, userName }) => {
      userNamesMap.set(userId, userName || "Unknown User");
    });
  }

  // Add data rows with user names and auto-incremented ID
  let serialNumber = 1; // Start auto-increment from 1

  for (const ingredient of ingredients) {
    const ingredientData = ingredient.toJSON();

    const allergens: any[] = [];
    const dietary: any[] = [];
    const cuisines: any[] = [];

    if (ingredientData.attributes) {
      ingredientData.attributes.forEach((attr: any) => {
        switch (attr.attribute_type) {
          case "allergen":
            allergens.push(attr.attribute_title);
            break;
          case "dietary":
            dietary.push(attr.attribute_title);
            break;
          case "cuisine":
            cuisines.push(attr.attribute_title);
            break;
        }
      });
    }

    // Get user full names from cache to prevent N+1 queries
    const createdByName = ingredientData.created_by
      ? userNamesMap.get(ingredientData.created_by) || "-"
      : "-";
    const updatedByName = ingredientData.updated_by
      ? userNamesMap.get(ingredientData.updated_by) || "-"
      : "-";

    // Format dates with time
    const formatDateTime = (dateString: string) => {
      if (!dateString) return "-";
      const date = new Date(dateString);
      return date.toLocaleString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      });
    };

    const createdAt = formatDateTime(ingredientData.created_at);
    const updatedAt = formatDateTime(ingredientData.updated_at);

    const dataRow = worksheet.addRow([
      serialNumber, // Use auto-incremented serial number instead of database ID
      ingredientData.ingredient_name || "-",
      ingredientData.ingredient_description || "-",
      ingredientData.ingredient_status || "-",
      ingredientData.cost_per_unit || "-",
      ingredientData.unit?.unit_title || "-",
      ingredientData.categories
        ?.map((cat: any) => cat.category_name)
        .join(", ") || "-",
      allergens.join(", ") || "-",
      dietary.join(", ") || "-",
      cuisines.join(", ") || "-",
      ingredientData.waste_percentage || "-",
      createdByName,
      updatedByName,
      createdAt,
      updatedAt,
    ]);

    // Add alternating row colors for better readability
    if (serialNumber % 2 === 0) {
      dataRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF8F9FA" }, // Light gray for even rows
      };
    }

    serialNumber++; // Increment for next row
  }

  // Auto-fit columns based on content with minimum and maximum widths
  worksheet.columns.forEach((column, index) => {
    let maxLength = 0;
    const columnHeader = headers[index];

    // Check header length
    if (columnHeader) {
      maxLength = Math.max(maxLength, columnHeader.length);
    }

    // Check data length in each cell of this column
    if (column && column.eachCell) {
      column.eachCell({ includeEmpty: false }, (cell) => {
        const cellValue = cell.value ? cell.value.toString() : "";
        maxLength = Math.max(maxLength, cellValue.length);
      });
    }

    // Set width with minimum of 12 and maximum of 40 characters
    const calculatedWidth = Math.min(Math.max(maxLength + 3, 12), 40);
    if (column) {
      column.width = calculatedWidth;
    }
  });

  // Add professional borders and formatting to all data cells
  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber === 1) {
      // Organization name row - special formatting
      row.eachCell((cell) => {
        cell.border = {
          bottom: { style: "thick", color: { argb: "FF135e96" } },
        };
      });
    } else if (rowNumber > 5) {
      // Data rows (skip organization name and info rows)
      row.eachCell((cell, colNumber) => {
        // Professional borders
        cell.border = {
          top: { style: "thin", color: { argb: "FFD1D5DB" } },
          left: { style: "thin", color: { argb: "FFD1D5DB" } },
          bottom: { style: "thin", color: { argb: "FFD1D5DB" } },
          right: { style: "thin", color: { argb: "FFD1D5DB" } },
        };

        // Different alignment for different columns
        if (colNumber === 1) {
          // ID column
          cell.alignment = { horizontal: "center", vertical: "middle" };
          cell.font = { bold: true, size: 10 };
        } else if (colNumber === 5) {
          // Cost column
          cell.alignment = { horizontal: "right", vertical: "middle" };
          cell.numFmt = "#,##0.00"; // Format as currency
        } else if (colNumber >= 14) {
          // Date columns
          cell.alignment = { horizontal: "center", vertical: "middle" };
          cell.font = { size: 9 };
        } else {
          cell.alignment = { horizontal: "left", vertical: "middle" };
        }

        // Add padding
        cell.alignment = {
          ...cell.alignment,
          indent: colNumber === 1 ? 0 : 1,
        };
      });

      // Set row height for better readability
      row.height = 20;
    } else if (rowNumber === 6) {
      // Header row - additional styling
      row.eachCell((cell) => {
        cell.border = {
          top: { style: "medium", color: { argb: "FF135e96" } },
          left: { style: "thin", color: { argb: "FFFFFFFF" } },
          bottom: { style: "medium", color: { argb: "FF135e96" } },
          right: { style: "thin", color: { argb: "FFFFFFFF" } },
        };
      });
    }
  });

  // Add freeze panes to keep headers visible when scrolling
  worksheet.views = [
    {
      state: "frozen",
      xSplit: 0,
      ySplit: 6, // Freeze at row 6 (header row)
      topLeftCell: "A7",
      activeCell: "A7",
    },
  ];

  // Add auto-filter to header row for enterprise functionality
  worksheet.autoFilter = {
    from: "A6",
    to: `O${6 + ingredients.length}`, // Adjust based on number of columns and rows
  };

  // Add print settings for professional printing
  worksheet.pageSetup = {
    paperSize: 9, // A4
    orientation: "landscape",
    fitToPage: true,
    fitToWidth: 1,
    fitToHeight: 0,
    margins: {
      left: 0.7,
      right: 0.7,
      top: 0.75,
      bottom: 0.75,
      header: 0.3,
      footer: 0.3,
    },
  };

  // Add header and footer for printing
  worksheet.headerFooter.oddHeader = `&C&"Arial,Bold"&14${orgName || "Organization"} - Ingredients Export`;
  worksheet.headerFooter.oddFooter = `&L&"Arial"&10Generated on: ${new Date().toLocaleString()}&R&"Arial"&10Page &P of &N`;

  // Set response headers
  const filename = `ingredients_export_${new Date().toISOString().split("T")[0]}.xlsx`;
  res.setHeader(
    "Content-Type",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  );
  res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

  await workbook.xlsx.write(res);
  res.end();
};

/**
 * @description Export ingredients to CSV format
 */
const exportToCSV = async (
  ingredients: any[],
  count: number,
  filterInfo: string,
  res: Response
): Promise<any> => {
  // CSV Headers
  const csvHeaders = [
    "ID",
    "Name",
    "Description",
    "Status",
    "Cost",
    "Unit of Measure",
    "Categories",
    "Allergens",
    "Dietary Info",
    "Cuisine",
    "Waste %",
    "Created By",
    "Updated By",
    "Created At",
    "Updated At",
  ];

  // Helper function to escape CSV values
  const escapeCSV = (value: any): string => {
    if (value === null || value === undefined) return "-";
    const str = String(value);
    if (str.includes(",") || str.includes('"') || str.includes("\n")) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };

  // Build CSV content
  let csvContent = `# Export Filters Applied: ${filterInfo}\n`;
  csvContent += `# Export Date: ${new Date().toISOString()}\n`;
  csvContent += `# Total Records: ${count}\n`;
  csvContent += "\n";
  csvContent += csvHeaders.join(",") + "\n";

  let serialNumber = 1; // Start auto-increment from 1

  for (const ingredient of ingredients) {
    const ingredientData = ingredient.toJSON();

    const allergens: any[] = [];
    const dietary: any[] = [];
    const cuisines: any[] = [];

    if (ingredientData.attributes) {
      ingredientData.attributes.forEach((attr: any) => {
        switch (attr.attribute_type) {
          case "allergen":
            allergens.push(attr.attribute_title);
            break;
          case "dietary":
            dietary.push(attr.attribute_title);
            break;
          case "cuisine":
            cuisines.push(attr.attribute_title);
            break;
        }
      });
    }

    // Get user full names for created_by and updated_by
    const createdByName = ingredientData.created_by
      ? (await getUserFullName(ingredientData.created_by)) || "-"
      : "-";
    const updatedByName = ingredientData.updated_by
      ? (await getUserFullName(ingredientData.updated_by)) || "-"
      : "-";

    // Format dates with time for CSV
    const formatDateTime = (dateString: string) => {
      if (!dateString) return "-";
      const date = new Date(dateString);
      return date.toLocaleString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      });
    };

    const createdAt = formatDateTime(ingredientData.created_at);
    const updatedAt = formatDateTime(ingredientData.updated_at);

    const row = [
      serialNumber, // Use auto-incremented serial number instead of database ID
      ingredientData.ingredient_name || "-",
      ingredientData.ingredient_description || "-",
      ingredientData.ingredient_status || "-",
      ingredientData.cost_per_unit || "-",
      ingredientData.unit?.unit_title || "-",
      ingredientData.categories
        ?.map((cat: any) => cat.category_name)
        .join("; ") || "-",
      allergens.join("; ") || "-",
      dietary.join("; ") || "-",
      cuisines.join("; ") || "-",
      ingredientData.waste_percentage || "-",
      createdByName,
      updatedByName,
      createdAt,
      updatedAt,
    ];

    csvContent += row.map(escapeCSV).join(",") + "\n";
    serialNumber++; // Increment for next row
  }

  // Set response headers
  const filename = `ingredients_export_${new Date().toISOString().split("T")[0]}.csv`;
  res.setHeader("Content-Type", "text/csv");
  res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

  res.send(csvContent);
};

/**
 * @description Get all ingredients with their relations
 * @route GET /api/v1/ingredients
 * @access Private
 */
const getIngredients = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      // organization_id,
      ingredient_status,
      category,
      allergy,
      cuisine,
      dietary,
      search,
      sort_by,
      sort_order = "ASC",
      page,
      size,
      download, // New parameter for export functionality
    }: any = req.query;

    const { limit, offset } = getPagination(Number(page), Number(size));

    // Build where clause for main ingredient table
    const whereClause: any = {};

    // Handle organization filtering based on user role
    const { isDefaultAccess } = await import("../helper/common");
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    if (hasDefaultAccess) {
      console.log("hasDefaultAccess", hasDefaultAccess);
      // Admin users can see all ingredients or filter by organization if specified
      const organizationId = req.query.organization_id;
      if (organizationId !== undefined) {
        console.log("organizationId", organizationId);
        if (organizationId === "null" || organizationId === "") {
          console.log("organizationId is null");
          whereClause.organization_id = null; // System defaults
        } else {
          console.log("organizationId is not null");
          whereClause.organization_id = organizationId;
        }
      }
      // If no organization specified, show all ingredients (no filter)
    } else {
      console.log("does not have default access");
      if(req.user.organization_id){
        // Regular users see only their organization's ingredients
        whereClause.organization_id = req.user.organization_id;
      }

    }
    // Handle ingredient status filtering
    if (ingredient_status) {
      if (ingredient_status === "active") {
        whereClause.ingredient_status = IngredientStatus.active;
      } else if (ingredient_status === "inactive") {
        whereClause.ingredient_status = IngredientStatus.inactive;
      }
    }

    // Handle search by ingredient name (MySQL compatible with case-insensitive)
    if (search) {
      const searchTerm = String(search).toLowerCase();
      whereClause[Op.or] = [
        sequelize.where(
          sequelize.fn("LOWER", sequelize.col("ingredient_name")),
          "LIKE",
          `%${searchTerm}%`
        ),
        sequelize.where(
          sequelize.fn("LOWER", sequelize.col("ingredient_description")),
          "LIKE",
          `%${searchTerm}%`
        ),
      ];
    }

    // Helper function to parse multiple IDs
    const parseMultipleIds = (param: any): number[] => {
      if (!param) return [];
      if (typeof param === "string") {
        return param
          .split(",")
          .map((id) => parseInt(id.trim()))
          .filter((id) => !isNaN(id));
      }
      if (Array.isArray(param)) {
        return param
          .map((id) => parseInt(String(id)))
          .filter((id) => !isNaN(id));
      }
      return [parseInt(String(param))].filter((id) => !isNaN(id));
    };

    // Build include array with filtering
    const includeArray: any[] = [
      {
        model: Category,
        as: "categories",
        through: {
          where: {
            ingredient_category_status: IngredientCategoryStatus.active,
          },
          attributes: [], // Exclude all junction table attributes including id
        },
        attributes: ["id", "category_name", "category_slug"],
        // Ensure only active categories are included by default
        where: {
          category_status: CategoryStatus.active,
        },
        required: false,
      },
      {
        model: FoodAttributes,
        as: "attributes",
        through: {
          where: {
            ingredient_attributes_status: IngredientAttributesStatus.active,
          },
          attributes: ["unit", "unit_of_measure"], // Include nutrition values from junction table
        },
        attributes: [
          "id",
          "attribute_title",
          "attribute_slug",
          "attribute_type",
        ],
        // Only active attributes are included in ingredient list
        where: {
          attribute_status: AttributeStatus.active,
        },
        required: false,
      },
      {
        model: RecipeMeasure,
        as: "unit",
        attributes: ["id", "unit_title", "unit_slug"],
        // Ensure only active units are included by default
        where: {
          status: MeasureStatus.active,
        },
        required: false,
      },
    ];

    // Build attribute filters for WHERE clause
    const attributeFilters: any[] = [];

    // Collect all attribute IDs that need to be filtered
    const allergyIds = parseMultipleIds(allergy);
    const cuisineIds = parseMultipleIds(cuisine);
    const dietaryIds = parseMultipleIds(dietary);

    if (allergyIds.length > 0) {
      attributeFilters.push({
        id: { [Op.in]: allergyIds },
        attribute_type: "allergen",
      });
    }

    if (cuisineIds.length > 0) {
      attributeFilters.push({
        id: { [Op.in]: cuisineIds },
        attribute_type: "cuisine",
      });
    }

    if (dietaryIds.length > 0) {
      attributeFilters.push({
        id: { [Op.in]: dietaryIds },
        attribute_type: "dietary",
      });
    }

    // If we have attribute filters, add them to the FoodAttributes include
    if (attributeFilters.length > 0) {
      const attributeIndex = includeArray.findIndex(
        (inc) => inc.as === "attributes"
      );
      if (attributeIndex !== -1) {
        includeArray[attributeIndex].where = {
          attribute_status: AttributeStatus.active,
          [Op.or]: attributeFilters,
        };
        includeArray[attributeIndex].required = true;
      }
    }

    // Add category filter (supports multiple IDs: category=1,2,3)
    if (category) {
      const categoryIds = parseMultipleIds(category);
      if (categoryIds.length > 0) {
        const categoryIndex = includeArray.findIndex(
          (inc) => inc.as === "categories"
        );
        if (categoryIndex !== -1) {
          includeArray[categoryIndex].where = {
            id: { [Op.in]: categoryIds },
            category_status: CategoryStatus.active,
          };
          includeArray[categoryIndex].required = true;
        }
      }
    }

    // Build order clause with multiple sort support
    let orderClause: any[] = [["created_at", "DESC"]]; // Default order

    if (sort_by) {
      // Parse multiple sort fields
      const sortFields = String(sort_by)
        .split(",")
        .map((field) => field.trim());
      const sortOrders = String(sort_order)
        .split(",")
        .map((order) =>
          order.trim().toUpperCase() === "DESC" ? "DESC" : "ASC"
        );

      // Build order array for multiple sorts
      orderClause = [];

      sortFields.forEach((field, index) => {
        const order = sortOrders[index] || "ASC"; // Default to ASC if not enough order values

        switch (field) {
          case "name":
            orderClause.push(["ingredient_name", order]);
            break;
          case "cost":
            orderClause.push(["cost_per_unit", order]);
            break;
          case "category":
            orderClause.push([
              { model: Category, as: "categories" },
              "category_name",
              order,
            ]);
            break;
          case "status":
            orderClause.push(["ingredient_status", order]);
            break;
          case "created_at":
            orderClause.push(["created_at", order]);
            break;
          case "updated_at":
            orderClause.push(["updated_at", order]);
            break;
          default:
            // If invalid field, add created_at as fallback
            orderClause.push(["created_at", order]);
        }
      });

      // If no valid sort fields were processed, use default
      if (orderClause.length === 0) {
        orderClause = [["created_at", "DESC"]];
      }
    }
    console.log("whereClause",whereClause)
    // Build query options
    const queryOptions: any = {
      where: whereClause,
      include: includeArray,
      order: orderClause,
      distinct: true, // Important for accurate count with joins
    };

    // Add pagination if limit is provided
    if (size) {
      queryOptions.limit = Number(limit);
      queryOptions.offset = Number(offset);
    }

    // Get ingredients with count
    const { count, rows: ingredients } =
      await Ingredient.findAndCountAll(queryOptions);

    // Since unit_of_measure is now a string field, we don't need to fetch unit information

    // Clean the ingredients data
    const cleanIngredients = ingredients.map((ingredient: any) => {
      const ingredientData = ingredient.toJSON();

      // Separate attributes by type
      const allergens: any[] = [];
      const dietary: any[] = [];
      const cuisines: any[] = [];
      const nutrition: any[] = [];

      if (ingredientData.attributes) {
        ingredientData.attributes.forEach((attr: any) => {
          const cleanAttr = {
            id: attr.id,
            attribute_title: attr.attribute_title,
            attribute_slug: attr.attribute_slug,
          };

          switch (attr.attribute_type) {
            case "allergen":
              allergens.push(cleanAttr);
              break;
            case "dietary":
              dietary.push(cleanAttr);
              break;
            case "cuisine":
              cuisines.push(cleanAttr);
              break;
            case "nutrition":
              nutrition.push({
                ...cleanAttr,
                value: attr.IngredientAttributes?.unit || 0,
                unit_of_measure: attr.IngredientAttributes?.unit_of_measure || null,
              });
              break;
          }
        });
      }

      return {
        id: ingredientData.id,
        ingredient_name: ingredientData.ingredient_name,
        ingredient_slug: ingredientData.ingredient_slug,
        ingredient_description: ingredientData.ingredient_description,
        category:
          ingredientData.categories?.map((cat: any) => ({
            id: cat.id,
            category_name: cat.category_name,
          })) || [],
        allergy: allergens,
        dietary_info: dietary,
        cuisine: cuisines,
        nutrition: nutrition,
        ingredient_status: ingredientData.ingredient_status,
        cost: ingredientData.cost_per_unit,
        measure_of_cost: ingredientData.unit
          ? {
            id: ingredientData.unit.id,
            unit_title: ingredientData.unit.unit_title,
          }
          : null,
        created_by: ingredientData.created_by,
        updated_by: ingredientData.updated_by,
        created_at: ingredientData.created_at,
        updated_at: ingredientData.updated_at,
      };
    });

    // Check if download is requested
    if (download === "excel" || download === "csv") {
      // For export, we need all records without pagination
      const exportQueryOptions = {
        where: whereClause,
        include: includeArray,
        order: orderClause,
        distinct: true,
        // Remove pagination for export
      };

      const { count: totalCount, rows: allIngredients } =
        await Ingredient.findAndCountAll(exportQueryOptions);

      return await handleExport(
        req.query,
        allIngredients,
        totalCount,
        download,
        res,
        req.user?.organization_id
      );
    }

    const { total_pages } = getPaginatedItems(size, page, count || 0);

    // Generate import sample file URL for frontend
    const baseUrl =
      global.config.API_BASE_URL ||
      "https://staging.namastevillage.theeasyaccess.com";
    const importSampleFile = `${baseUrl}recipe_defaults/sample_files/ingredients/ingredient_import_template.xlsx`;

    // Build response in your standard format
    const response: any = {
      status: true,
      message: res.__("INGREDIENTS_FETCHED_SUCCESSFULLY"),
      count: count,
      data: cleanIngredients,
      page: Number(page),
      size: Number(size) || 0,
      total_pages: total_pages || 0,
      import_sample_file: importSampleFile,
    };

    return res.status(StatusCodes.OK).json(response);
  } catch (error: unknown) {
    console.log("Error in ingredients.controller.ts - getIngredients:", error);
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_INGREDIENTS"),
      error: customError.message,
    });
  }
};

/**
 * @description Get single ingredient by ID or slug with all relations
 * @route GET /api/v1/ingredients/:identifier
 * @access Private
 */
const getIngredientById = async (req: Request, res: Response): Promise<any> => {
  try {
    // Get identifier parameter (can be ID or slug)
    const identifier = req.params.id;
    if (!identifier) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_INGREDIENT_IDENTIFIER"),
        error: "Ingredient identifier (ID or slug) is required",
      });
    }

    // Handle organization filtering based on user role
    const { isDefaultAccess } = await import("../helper/common");
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Determine if identifier is numeric (ID) or string (slug)
    const isNumeric = !isNaN(Number(identifier));
    const whereCondition: any = {};

    // Apply organization filtering based on user role
    if (hasDefaultAccess) {
      // Admin users can access ingredients from any organization
      // Optionally filter by organization if specified in query
      const organizationId = req.query.organization_id;
      if (organizationId !== undefined) {
        if (organizationId === "null" || organizationId === "") {
          whereCondition.organization_id = null;
        } else {
          whereCondition.organization_id = organizationId;
        }
      }
      // If no organization specified, show all ingredients (no filter)
    } else {
      if(req.user.organization_id){
        // Regular users see only their organization's ingredients
        whereCondition.organization_id = req.user.organization_id;
      }

    }
    if (isNumeric) {
      whereCondition.id = Number(identifier);
    } else {
      whereCondition.ingredient_slug = identifier;
    }

    const ingredient = await Ingredient.findOne({
      where: whereCondition,
      include: [
        {
          model: Category,
          as: "categories",
          through: {
            where: {
              ingredient_category_status: IngredientCategoryStatus.active,
            },
            attributes: [], // Exclude all junction table attributes including id
          },
          attributes: [
            "id",
            "category_name",
            "category_slug",
            [
              sequelize.literal(
                `IF(category_icon IS NOT NULL AND category_icon != '', CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = category_icon)), '')`
              ),
              "category_icon_link",
            ],
            "category_icon",
          ],
          where: { category_status: CategoryStatus.active },
          required: false,
        },
        {
          model: FoodAttributes,
          as: "attributes",
          through: {
            where: {
              ingredient_attributes_status: IngredientAttributesStatus.active,
            },
            attributes: ["unit_of_measure", "unit"], // Exclude all junction table attributes including id
          },
          attributes: [
            "id",
            "attribute_title",
            "attribute_slug",
            "attribute_type",
            [
              sequelize.literal(
                `IF(attribute_icon IS NOT NULL AND attribute_icon != '', CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = attribute_icon)), '')`
              ),
              "attribute_icon_link",
            ],
            "attribute_icon"
          ],
          where: { attribute_status: AttributeStatus.active },
          required: false,
        },
        {
          model: RecipeMeasure,
          as: "unit",
          attributes: ["id", "unit_title", "unit_slug"],
          where: { status: MeasureStatus.active },
          required: false,
        },
        {
          model: IngredientConversion,
          as: "conversions",
          where: {
            ingredient_conversion_status: IngredientConversionStatus.active,
          },
          required: false,
          include: [
            {
              model: RecipeMeasure,
              as: "fromUnit",
              attributes: ["id", "unit_title", "unit_slug"],
            },
            {
              model: RecipeMeasure,
              as: "toUnit",
              attributes: ["id", "unit_title", "unit_slug"],
            },
          ],
        },
      ],
    });

    if (!ingredient) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("INGREDIENT_NOT_FOUND"),
        error: `Ingredient with identifier '${identifier}' not found in your organization`,
      });
    }

    // Format response with clean data structure
    const ingredientData = ingredient.toJSON() as any;

    // Separate attributes by type to match create/update API response format
    const nutrition_attributes: any[] = [];
    const allergy_attributes: any[] = [];
    const cuisine_attributes: any[] = [];
    const dietary_attributes: any[] = [];
    if (ingredientData.attributes) {
      ingredientData.attributes.forEach((attr: any) => {
        const cleanAttr = {
          id: attr.id,
          attribute_title: attr.attribute_title,
          attribute_slug: attr.attribute_slug,
          unit_of_measure: attr.IngredientAttributes?.unit_of_measure || null,
          unit: attr.IngredientAttributes?.unit || null,
          attribute_icon_link: attr.attribute_icon_link || null,
          attribute_icon: attr.attribute_icon || null,
        };

        switch (attr.attribute_type) {
          case "nutrition":
            nutrition_attributes.push(cleanAttr);
            break;
          case "allergen":
            allergy_attributes.push({
              id: attr.id,
              attribute_title: attr.attribute_title,
              attribute_slug: attr.attribute_slug,
              attribute_icon_link: attr.attribute_icon_link || null,
              attribute_icon: attr.attribute_icon || null,
            });
            break;
          case "cuisine":
            cuisine_attributes.push({
              id: attr.id,
              attribute_title: attr.attribute_title,
              attribute_slug: attr.attribute_slug,
              attribute_icon_link: attr.attribute_icon_link || null,
              attribute_icon: attr.attribute_icon || null,
            });
            break;
          case "dietary":
            dietary_attributes.push({
              id: attr.id,
              attribute_title: attr.attribute_title,
              attribute_slug: attr.attribute_slug,
              attribute_icon_link: attr.attribute_icon_link || null,
              attribute_icon: attr.attribute_icon || null,
            });
            break;
        }
      });
    }

    // Clean categories data
    const categories =
      ingredientData.categories?.map((cat: any) => ({
        id: cat.id,
        category_name: cat.category_name,
        category_slug: cat.category_slug,
        status: cat.IngredientCategory?.ingredient_category_status || "active",
        category_icon_link: cat.category_icon_link || null,
        category_icon: cat.category_icon || null,
      })) || [];

    // Clean conversions data

    // Debug: Direct query to check what's in the database
    const directConversions = await IngredientConversion.findAll({
      where: {
        ingredient_id: ingredient.id,
        ingredient_conversion_status: IngredientConversionStatus.active,
      },
      include: [
        {
          model: RecipeMeasure,
          as: "fromUnit",
          attributes: ["id", "unit_title", "unit_slug"],
        },
        {
          model: RecipeMeasure,
          as: "toUnit",
          attributes: ["id", "unit_title", "unit_slug"],
        },
      ],
    });

    // Clean unit data
    const unit = ingredientData.unit
      ? {
        id: ingredientData.unit.id,
        unit_title: ingredientData.unit.unit_title,
        unit_slug: ingredientData.unit.unit_slug,
      }
      : null;

    const responseData = {
      id: ingredientData.id,
      ingredient_name: ingredientData.ingredient_name,
      ingredient_slug: ingredientData.ingredient_slug,
      ingredient_description: ingredientData.ingredient_description,
      ingredient_status: ingredientData.ingredient_status,
      waste_percentage: ingredientData.waste_percentage,
      cost_per_unit: ingredientData.cost_per_unit,
      unit,
      categories,
      nutrition_attributes,
      allergy_attributes,
      cuisine_attributes,
      dietary_attributes,
      conversions: directConversions.length > 0 ? directConversions : [],
      created_at: ingredientData.created_at,
      updated_at: ingredientData.updated_at,
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("INGREDIENT_RETRIEVED_SUCCESSFULLY"),
      data: responseData,
    });
  } catch (error: unknown) {
    console.log(
      "Error in ingredients.controller.ts - getIngredientById:",
      error
    );
    const customError = error as any;

    // Handle specific error types
    if (customError.name === "SequelizeDatabaseError") {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("DATABASE_ERROR"),
        error: "Invalid query or database constraint violation",
      });
    }

    if (customError.name === "SequelizeConnectionError") {
      return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
        status: false,
        message: res.__("DATABASE_CONNECTION_ERROR"),
        error: "Unable to connect to database",
      });
    }

    // Generic error
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_INGREDIENT"),
      error:
        customError.message ||
        "Unknown error occurred while fetching ingredient",
    });
  }
};

/**
 * @description Update ingredient and its relations
 * @route PUT /api/v1/ingredients/:id
 * @access Private
 */
const updateIngredient = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const transaction = await transactionManager.start();

    const ingredient = await Ingredient.findByPk(req.params.id);
    if (!ingredient) {
      await transactionManager.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("INGREDIENT_NOT_FOUND"),
      });
    }

    const {
      ingredient_name,
      ingredient_description,
      ingredient_status,
      waste_percentage,
      unit_of_measure,
      cost_per_unit,
      categories,
      nutrition_attributes,
      allergy_attributes,
      dietary_attributes,
      conversions,
    } = req.body;

    // Validate and set ingredient status
    let validatedStatus = ingredient.ingredient_status; // Keep current status by default
    if (ingredient_status) {
      if (ingredient_status === "active") {
        validatedStatus = IngredientStatus.active;
      } else if (ingredient_status === "inactive") {
        validatedStatus = IngredientStatus.inactive;
      }
      // If invalid status provided, keep current status
    }

    // Generate new slug if ingredient name is being updated
    let ingredient_slug = ingredient.ingredient_slug;
    if (ingredient_name && ingredient_name !== ingredient.ingredient_name) {
      const checkSlugExists = async (slug: string): Promise<boolean> => {
        const existingIngredient = await Ingredient.findOne({
          where: {
            ingredient_slug: slug,
            organization_id: ingredient.organization_id,
            id: { [Op.ne]: ingredient.id }, // Exclude current ingredient
          },
          transaction, // Use transaction to prevent race conditions
          lock: true, // Add row-level lock for consistency
        });
        return !!existingIngredient;
      };

      ingredient_slug = await generateUniqueSlug(
        ingredient_name,
        checkSlugExists,
        {
          maxLength: 25,
          separator: "-",
          lowercase: true,
        }
      );
    }

    // Check if cost_per_unit is being updated
    const isCostUpdated =
      cost_per_unit !== undefined &&
      parseFloat(cost_per_unit) !== parseFloat(ingredient.cost_per_unit);

    // Prepare update data
    const updateData: any = {
      ingredient_name,
      ingredient_slug,
      ingredient_description,
      ingredient_status: validatedStatus,
      waste_percentage,
      unit_of_measure,
      cost_per_unit,
      updated_by: req.user.id,
    };

    // Add cost timestamp if cost is being updated
    if (isCostUpdated) {
      updateData.cost_last_updated_at = new Date();
    }

    // Update ingredient
    await ingredient.update(updateData, { transaction });

    // Update categories if provided
    if (categories !== undefined) {
      // First, make all existing categories inactive
      await IngredientCategory.update(
        { ingredient_category_status: IngredientCategoryStatus.inactive },
        { where: { ingredient_id: ingredient.id }, transaction }
      );

      if (categories.length > 0) {
        // Check for existing categories and reactivate them, create new ones if needed
        for (const categoryId of categories) {
          const existingCategory = await IngredientCategory.findOne({
            where: { ingredient_id: ingredient.id, category_id: categoryId },
            transaction,
          });

          if (existingCategory) {
            // Reactivate existing category
            await existingCategory.update(
              {
                ingredient_category_status: IngredientCategoryStatus.active,
                updated_by: req.user.id,
              },
              { transaction }
            );
          } else {
            // Create new category relation
            await IngredientCategory.create(
              {
                category_id: categoryId,
                ingredient_id: ingredient.id,
                ingredient_category_status: IngredientCategoryStatus.active,
                organization_id: ingredient.organization_id,
                created_by: req.user.id,
                updated_by: req.user.id,
              },
              { transaction }
            );
          }
        }
      }
    }

    // Update attributes if any are provided
    if (
      nutrition_attributes !== undefined ||
      allergy_attributes !== undefined ||
      dietary_attributes !== undefined
    ) {
      // First, make all existing attributes inactive
      await IngredientAttributes.update(
        { ingredient_attributes_status: IngredientAttributesStatus.inactive },
        { where: { ingredient_id: ingredient.id }, transaction }
      );

      // Collect all new attributes to process
      const allNewAttributes: any[] = [];

      // Add nutrition attributes if provided (with unit & unit_of_measure)
      if (nutrition_attributes && nutrition_attributes.length > 0) {
        nutrition_attributes.forEach((attr: any) => {
          allNewAttributes.push({
            id: attr.attribute_id,
            unit_of_measure: attr.unit_of_measure,
            unit: attr.unit,
            type: "nutrition",
          });
        });
      }

      // Add allergy attributes if provided (just IDs)
      if (allergy_attributes && allergy_attributes.length > 0) {
        allergy_attributes.forEach((id: number) => {
          allNewAttributes.push({
            id,
            type: "allergy",
          });
        });
      }

      // Add dietary attributes if provided (just IDs)
      if (dietary_attributes && dietary_attributes.length > 0) {
        dietary_attributes.forEach((id: number) => {
          allNewAttributes.push({
            id,
            type: "dietary",
          });
        });
      }

      // Process each attribute - reactivate existing or create new
      for (const attr of allNewAttributes) {
        const existingAttribute = await IngredientAttributes.findOne({
          where: {
            ingredient_id: ingredient.id,
            attributes_id: attr.id,
          },
          transaction,
        });

        if (existingAttribute) {
          // Reactivate existing attribute with updated values
          await existingAttribute.update(
            {
              ingredient_attributes_status: IngredientAttributesStatus.active,
              unit_of_measure: attr.unit_of_measure || null,
              unit: attr.unit || null,
              updated_by: req.user.id,
            },
            { transaction }
          );
        } else {
          // Create new attribute relation
          await IngredientAttributes.create(
            {
              ingredient_id: ingredient.id,
              attributes_id: attr.id,
              unit_of_measure: attr.unit_of_measure || null,
              unit: attr.unit || null,
              ingredient_attributes_status: IngredientAttributesStatus.active,
              organization_id: ingredient.organization_id,
              created_by: req.user.id,
              updated_by: req.user.id,
            },
            { transaction }
          );
        }
      }

      // Update nutrition timestamp if nutrition attributes were updated
      if (allNewAttributes.some((attr) => attr.type === "nutrition")) {
        await updateIngredientNutritionTimestamp(ingredient.id, transaction);
      }
    }

    // Update conversions if provided
    if (conversions !== undefined) {
      // Remove all existing conversions for this ingredient
      await IngredientConversion.destroy({
        where: { ingredient_id: ingredient.id },
        transaction,
      });

      if (conversions.length > 0) {
        const conversionMaps = conversions.map((conv: any) => ({
          ingredient_id: ingredient.id,
          from_measure: conv.from_measure,
          from_measure_value: conv.from_measure_value,
          to_measure: conv.to_measure,
          to_measure_value: conv.to_measure_value,
          ingredient_conversion_status: IngredientConversionStatus.active,
          organization_id: ingredient.organization_id,
          created_by: req.user.id,
          updated_by: req.user.id,
        }));
        await IngredientConversion.bulkCreate(conversionMaps, { transaction });
      }
    }

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("INGREDIENT_UPDATED_SUCCESSFULLY"),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error updating ingredient"
    );
  }
};

/**
 * @description Delete ingredient (soft delete)
 * @route DELETE /api/v1/ingredients/:id
 * @access Private
 */
const deleteIngredient = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const transaction = await transactionManager.start();

    const ingredient = await Ingredient.findByPk(req.params.id);
    if (!ingredient) {
      await transactionManager.rollback();
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("INGREDIENT_NOT_FOUND"),
      });
    }

    // Check if ingredient is being used in recipes (any status)
    const recipeUsage = await RecipeIngredients.count({
      where: {
        ingredient_id: req.params.id,
      },
      transaction,
    });

    // Check if ingredient is being used in conversions (any status)
    const conversionUsage = await IngredientConversion.count({
      where: {
        ingredient_id: req.params.id,
      },
      transaction,
    });

    const totalUsage = recipeUsage + conversionUsage;

    // If ingredient is in use, prevent deletion
    if (totalUsage > 0) {
      await transactionManager.rollback();

      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INGREDIENT_IN_USE_CANNOT_DELETE"),
      });
    }

    // Hard delete ingredient
    await ingredient.destroy({ transaction });

    // Hard delete all relations when ingredient is deleted
    await IngredientCategory.destroy({
      where: { ingredient_id: ingredient.id },
      transaction,
    });

    await IngredientAttributes.destroy({
      where: { ingredient_id: ingredient.id },
      transaction,
    });

    await IngredientConversion.destroy({
      where: { ingredient_id: ingredient.id },
      transaction,
    });

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("INGREDIENT_DELETED_SUCCESSFULLY"),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting ingredient"
    );
  }
};

/**
 * @description Search ingredients
 * @route GET /api/v1/ingredients/search
 * @access Private
 */
const searchIngredients = async (req: any, res: Response): Promise<any> => {
  try {
    const { query, organization_id, status, page, limit } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    // Build where clause with proper admin user handling
    const whereClause: any = {};

    // Handle organization filtering based on user role
    const { isDefaultAccess } = await import("../helper/common");
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    if (hasDefaultAccess) {
      // Admin users can search all ingredients or filter by organization if specified
      if (organization_id !== undefined) {
        if (organization_id === "null" || organization_id === "") {
          whereClause.organization_id = null; // System defaults
        } else {
          whereClause.organization_id = organization_id;
        }
      }
      // If no organization specified, show all ingredients (no filter)
    } else {
      // Regular users see only their organization's ingredients
      whereClause.organization_id = req.user.organization_id;
    }

    // Handle status filtering - accept both 'active' and 'inactive' from FE
    if (status) {
      if (status === "active") {
        whereClause.ingredient_status = IngredientStatus.active;
      } else if (status === "inactive") {
        whereClause.ingredient_status = IngredientStatus.inactive;
      }
    } else {
      // Default to active if no status provided
      whereClause.ingredient_status = IngredientStatus.active;
    }

    if (query) {
      whereClause[Op.or] = [
        { ingredient_name: { [Op.like]: `%${query}%` } },
        { ingredient_description: { [Op.like]: `%${query}%` } },
      ];
    }

    // Search ingredients with count
    const { count, rows: ingredients } = await Ingredient.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: "categories",
          through: {
            where: {
              ingredient_category_status: IngredientCategoryStatus.active,
            },
            attributes: [], // Exclude all junction table attributes including id
          },
          attributes: ["id", "category_name", "category_slug"],
        },
        {
          model: RecipeMeasure,
          as: "unit",
          attributes: ["id", "unit_title", "unit_slug"],
        },
      ],
      limit: Number(limit),
      offset,
      order: [["ingredient_name", "ASC"]],
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("INGREDIENTS_SEARCHED_SUCCESSFULLY"),
      data: ingredients,
      pagination: {
        total: count,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(count / Number(limit)),
      },
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_SEARCHING_INGREDIENTS"),
      error: customError.message,
    });
  }
};

/**
 * @description Import ingredients from Excel file
 * @route POST /api/v1/ingredients/import
 * @access Private
 */
const importIngredients = async (req: any, res: Response): Promise<any> => {
  const transaction = await sequelize.transaction();

  try {
    // Check if file is uploaded
    if (!req.file) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("NO_FILE_UPLOADED"),
      });
    }

    // Check if user has default access (admin users)
    const { isDefaultAccess } = await import("../helper/common");
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Determine organization ID based on user access
    let organization_id;
    if (hasDefaultAccess) {
      // Admin users can specify organization_id in body or default to null for system-wide
      organization_id = req.body.organization_id || null;
    } else {
      // Regular users must use their organization_id
      organization_id = req.user?.organization_id;
      if (!organization_id) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ORGANIZATION_ID_REQUIRED"),
        });
      }
    }

    // Read Excel file
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(req.file.buffer);
    const worksheet = workbook.getWorksheet(1); // Get first worksheet

    if (!worksheet) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_EXCEL_FILE"),
      });
    }

    const results = {
      total: 0,
      success: 0,
      failed: 0,
      errors: [] as any[],
    };

    // Expected columns (row 1 should contain headers)
    const expectedHeaders = [
      "ingredient_name",
      "ingredient_description",
      "ingredient_status",
      "waste_percentage",
      "cost_per_unit",
      "unit_of_measure", // unit name instead of ID
      "categories", // comma-separated category names
      "nutritions", // comma-separated nutrition attribute names with units (format: "name:unit_name:unit")
      "allergens", // comma-separated allergen attribute names
      "dietary", // comma-separated dietary attribute names
    ];

    // Validate headers
    const headerRow = worksheet.getRow(1);
    const headers: string[] = [];
    headerRow.eachCell((cell, colNumber) => {
      headers[colNumber - 1] = String(cell.value).trim();
    });

    // Check if all expected headers are present
    const missingHeaders = expectedHeaders.filter(
      (header) => !headers.includes(header)
    );
    if (missingHeaders.length > 0) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("MISSING_REQUIRED_HEADERS"),
        missing_headers: missingHeaders,
        expected_headers: expectedHeaders,
      });
    }

    // Process each row (starting from row 2)
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // Skip empty rows
      if (row.hasValues) {
        results.total++;

        try {
          // Extract data from row
          const rowData: any = {};
          headers.forEach((header, index) => {
            const cell = row.getCell(index + 1);
            rowData[header] = cell.value;
          });

          // Helper functions to lookup IDs by names with organization_id and system defaults

          const findCategoriesByNames = async (categoryNames: string[]) => {
            const categories = await Category.findAll({
              where: {
                [Op.and]: [
                  {
                    [Op.or]: [
                      { category_name: { [Op.in]: categoryNames } },
                      {
                        category_slug: {
                          [Op.in]: categoryNames.map((name) =>
                            name.toLowerCase().replace(/\s+/g, "-")
                          ),
                        },
                      },
                    ],
                  },
                  {
                    [Op.or]: [
                      { organization_id },
                      { organization_id: null }, // System defaults
                      { is_system_category: true }, // System categories
                    ],
                  },
                ],
              },
            });
            return categories;
          };

          const findAttributesByNames = async (
            attributeNames: string[],
            attributeType?: string
          ) => {
            const whereClause: any = {
              [Op.and]: [
                {
                  [Op.or]: [
                    { attribute_title: { [Op.in]: attributeNames } },
                    {
                      attribute_slug: {
                        [Op.in]: attributeNames.map((name) =>
                          name.toLowerCase().replace(/\s+/g, "-")
                        ),
                      },
                    },
                  ],
                },
                {
                  [Op.or]: [
                    { organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_attribute: true }, // System attributes
                  ],
                },
              ],
            };

            if (attributeType) {
              whereClause[Op.and].push({ attribute_type: attributeType });
            }

            const attributes = await FoodAttributes.findAll({
              where: whereClause,
            });
            return attributes;
          };

          // Validate required fields
          if (
            !rowData.ingredient_name ||
            !rowData.cost_per_unit ||
            !rowData.unit_of_measure
          ) {
            throw new Error(
              `Row ${rowNumber}: Missing required fields (ingredient_name, cost_per_unit, unit_of_measure)`
            );
          }

          // Validate ingredient status
          const ingredientStatus = rowData.ingredient_status
            ?.toString()
            .toLowerCase();
          if (
            ingredientStatus &&
            !["active", "inactive"].includes(ingredientStatus)
          ) {
            throw new Error(
              `Row ${rowNumber}: Invalid ingredient_status. Must be 'active' or 'inactive'`
            );
          }

          // Validate and lookup unit of measure (main ingredient table still uses foreign key)
          const unitOfMeasureName = String(rowData.unit_of_measure).trim();
          if (!unitOfMeasureName) {
            throw new Error(
              `Row ${rowNumber}: Unit of measure cannot be empty`
            );
          }

          // Find unit by name for main ingredient table (still uses foreign key)
          const unit = await RecipeMeasure.findOne({
            where: {
              [Op.and]: [
                {
                  [Op.or]: [
                    { unit_title: { [Op.like]: `%${unitOfMeasureName}%` } },
                    {
                      unit_slug: unitOfMeasureName.toLowerCase().replace(/\s+/g, "-"),
                    },
                  ],
                },
                {
                  [Op.or]: [
                    { organization_id },
                    { organization_id: null }, // System defaults
                    { is_system_unit: true }, // System units
                  ],
                },
              ],
            },
          });

          if (!unit) {
            throw new Error(
              `Row ${rowNumber}: Unit of measure '${unitOfMeasureName}' not found. Please check the unit name or create it first.`
            );
          }

          // Generate slug
          const ingredient_slug = generateSlug(rowData.ingredient_name);

          // Create ingredient
          const ingredient = await Ingredient.create(
            {
              ingredient_name: rowData.ingredient_name,
              ingredient_slug,
              ingredient_description: rowData.ingredient_description || null,
              ingredient_status:
                ingredientStatus === "inactive"
                  ? IngredientStatus.inactive
                  : IngredientStatus.active,
              waste_percentage: rowData.waste_percentage
                ? parseFloat(String(rowData.waste_percentage))
                : null,
              cost_per_unit: parseFloat(String(rowData.cost_per_unit)),
              unit_of_measure: unit.id, // Main ingredient table uses foreign key
              organization_id,
              created_by: req.user.id,
              updated_by: req.user.id,
            },
            { transaction }
          );

          // Process categories by names
          if (rowData.categories) {
            const categoryNames = String(rowData.categories)
              .split(",")
              .map((name) => name.trim())
              .filter((name) => name);

            if (categoryNames.length > 0) {
              const foundCategories =
                await findCategoriesByNames(categoryNames);
              const foundCategoryNames = foundCategories.map(
                (cat: any) => cat.category_name
              );
              const missingCategories = categoryNames.filter(
                (name) =>
                  !foundCategoryNames.some(
                    (foundName: string) =>
                      foundName.toLowerCase() === name.toLowerCase()
                  )
              );

              if (missingCategories.length > 0) {
                console.warn(
                  `Row ${rowNumber}: Categories not found: ${missingCategories.join(", ")}`
                );
              }

              if (foundCategories.length > 0) {
                const categoryMaps = foundCategories.map((category: any) => ({
                  category_id: category.id,
                  ingredient_id: ingredient.id,
                  ingredient_category_status: IngredientCategoryStatus.active,
                  organization_id,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                }));
                await IngredientCategory.bulkCreate(categoryMaps, {
                  transaction,
                });
              }
            }
          }

          // Collect all attributes for single bulkCreate
          const allAttributeMaps: any[] = [];

          // Process nutrition attributes (format: "name:unit_name:unit")
          if (rowData.nutritions) {
            const nutritionEntries = String(rowData.nutritions).split(",");
            for (const entry of nutritionEntries) {
              const parts = entry.trim().split(":");
              if (parts.length >= 1) {
                const attributeName = parts[0].trim();
                const unitName = parts[1] ? parts[1].trim() : null;
                const unitValue = parts[2] ? parseFloat(parts[2].trim()) : null;

                // Find nutrition attribute by name
                const nutritionAttributes = await findAttributesByNames(
                  [attributeName],
                  "nutrition"
                );
                if (nutritionAttributes.length === 0) {
                  console.warn(
                    `Row ${rowNumber}: Nutrition attribute '${attributeName}' not found`
                  );
                  continue;
                }

                const attribute = nutritionAttributes[0];

                // Store unit name directly as string (no lookup needed)
                allAttributeMaps.push({
                  ingredient_id: ingredient.id,
                  attributes_id: attribute.id,
                  unit_of_measure: unitName || null, // Store unit name directly as string
                  unit: unitValue,
                  ingredient_attributes_status:
                    IngredientAttributesStatus.active,
                  organization_id,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                });
              }
            }
          }

          // Process allergen attributes (by names)
          if (rowData.allergens) {
            const allergenNames = String(rowData.allergens)
              .split(",")
              .map((name) => name.trim())
              .filter((name) => name);

            if (allergenNames.length > 0) {
              const allergenAttributes = await findAttributesByNames(
                allergenNames,
                "allergen"
              );
              const foundAllergenNames = allergenAttributes.map(
                (attr: any) => attr.attribute_title
              );
              const missingAllergens = allergenNames.filter(
                (name) =>
                  !foundAllergenNames.some(
                    (foundName: string) =>
                      foundName.toLowerCase() === name.toLowerCase()
                  )
              );

              if (missingAllergens.length > 0) {
                console.warn(
                  `Row ${rowNumber}: Allergen attributes not found: ${missingAllergens.join(", ")}`
                );
              }

              allergenAttributes.forEach((attribute: any) => {
                allAttributeMaps.push({
                  ingredient_id: ingredient.id,
                  attributes_id: attribute.id,
                  ingredient_attributes_status:
                    IngredientAttributesStatus.active,
                  organization_id,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                });
              });
            }
          }

          // Process dietary attributes (by names)
          if (rowData.dietary) {
            const dietaryNames = String(rowData.dietary)
              .split(",")
              .map((name) => name.trim())
              .filter((name) => name);

            if (dietaryNames.length > 0) {
              const dietaryAttributes = await findAttributesByNames(
                dietaryNames,
                "dietary"
              );
              const foundDietaryNames = dietaryAttributes.map(
                (attr: any) => attr.attribute_title
              );
              const missingDietary = dietaryNames.filter(
                (name) =>
                  !foundDietaryNames.some(
                    (foundName: string) =>
                      foundName.toLowerCase() === name.toLowerCase()
                  )
              );

              if (missingDietary.length > 0) {
                console.warn(
                  `Row ${rowNumber}: Dietary attributes not found: ${missingDietary.join(", ")}`
                );
              }

              dietaryAttributes.forEach((attribute: any) => {
                allAttributeMaps.push({
                  ingredient_id: ingredient.id,
                  attributes_id: attribute.id,
                  ingredient_attributes_status:
                    IngredientAttributesStatus.active,
                  organization_id,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                });
              });
            }
          }

          // Single bulkCreate for all attributes
          if (allAttributeMaps.length > 0) {
            await IngredientAttributes.bulkCreate(allAttributeMaps, {
              transaction,
            });
          }

          // Log successful import details
          console.log(
            `Row ${rowNumber}: Successfully imported '${rowData.ingredient_name}' with ${allAttributeMaps.length} attributes`
          );
          results.success++;
        } catch (error: any) {
          results.failed++;
          results.errors.push({
            row: rowNumber,
            error: error.message,
          });

          // For critical errors, rollback entire transaction
          if (
            error.name === "SequelizeConnectionError" ||
            error.name === "SequelizeDatabaseError"
          ) {
            throw error; // This will trigger the outer catch block
          }

          // For validation errors, continue with other rows
          console.warn(`Row ${rowNumber} failed validation:`, error.message);
        }
      }
    }

    await transaction.commit();

    // Add summary message based on results
    let message = res.__("INGREDIENTS_IMPORTED_SUCCESSFULLY");
    if (results.failed > 0) {
      message += ` ${results.success} successful, ${results.failed} failed.`;
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message,
      results,
      summary: {
        total_processed: results.total,
        successful_imports: results.success,
        failed_imports: results.failed,
        success_rate:
          results.total > 0
            ? Math.round((results.success / results.total) * 100)
            : 0,
      },
    });
  } catch (error: unknown) {
    await transaction.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error importing ingredients"
    );
  }
};

// Export functionality will be integrated into getIngredients function

/**
 * @description Download Excel template for ingredient import
 * @route GET /api/v1/ingredients/import-template
 * @access Private
 */
const downloadImportTemplate = async (
  _req: Request,
  res: Response
): Promise<any> => {
  try {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Ingredients Template");

    // Define headers
    const headers = [
      "ingredient_name",
      "ingredient_description",
      "ingredient_status",
      "waste_percentage",
      "cost_per_unit",
      "unit_of_measure",
      "categories",
      "nutritions",
      "allergens",
      "dietary",
    ];

    // Add headers to the first row
    worksheet.addRow(headers);

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Add sample data row
    const sampleData = [
      "Chicken Breast",
      "Fresh organic chicken breast",
      "active",
      "5.5",
      "12.99",
      "Grams",
      "Protein,Meat,Poultry",
      "Protein:Grams:25.5,Calories:Kcal:165",
      "Gluten Free,Dairy Free",
      "Organic,High Protein",
    ];
    worksheet.addRow(sampleData);

    // Add instructions in separate rows
    worksheet.addRow([]);
    worksheet.addRow(["INSTRUCTIONS:"]);
    worksheet.addRow(["ingredient_name: Required - Name of the ingredient"]);
    worksheet.addRow([
      "ingredient_description: Optional - Description of the ingredient",
    ]);
    worksheet.addRow([
      'ingredient_status: Optional - "active" or "inactive" (default: active)',
    ]);
    worksheet.addRow([
      "waste_percentage: Optional - Percentage of waste (0-100)",
    ]);
    worksheet.addRow([
      "cost_per_unit: Required - Cost per unit (decimal number)",
    ]);
    worksheet.addRow([
      'unit_of_measure: Required - Name of the unit (e.g., "Grams", "Milliliters")',
    ]);
    worksheet.addRow([
      'categories: Optional - Comma-separated category names (e.g., "Protein,Meat")',
    ]);
    worksheet.addRow([
      'nutritions: Optional - Format: "name:unit:value" (e.g., "Protein:Grams:25.5,Calories:Kcal:165")',
    ]);
    worksheet.addRow([
      'allergens: Optional - Comma-separated allergen names (e.g., "Gluten Free,Dairy Free")',
    ]);
    worksheet.addRow([
      'dietary: Optional - Comma-separated dietary names (e.g., "Organic,Vegan")',
    ]);

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      column.width = 20;
    });

    // Set response headers for file download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      "attachment; filename=ingredients_import_template.xlsx"
    );

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_GENERATING_TEMPLATE"),
      error: customError.message,
    });
  }
};

export default {
  createIngredient,
  getIngredients,
  getIngredientById,
  updateIngredient,
  deleteIngredient,
  searchIngredients,
  importIngredients,
  downloadImportTemplate,
};
