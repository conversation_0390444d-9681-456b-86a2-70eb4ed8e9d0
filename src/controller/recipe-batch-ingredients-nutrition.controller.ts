import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { db } from "../models";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { RecipeIngredientsNutritionCuisineData } from "../types/recipe-batch.types";
import { createRecipeHistory } from "../helper/recipe.helper";
import {
  updateRecipeCostTimestamp,
  updateRecipeNutritionTimestamp,
} from "../helper/timestamp.helper";
import {
  TransactionManager,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import {
  checkBatchAuthorization,
  validateRecipeId,
  validateRecipeAccess,
  updateRecipeTimestamp,
  createSuccessResponse,
  createHistoryData,
} from "../helper/recipe-batch-common.helper";

// Get models from db object to ensure associations are set up
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;

/**
 * @description Handle ingredients, nutrition, and cuisine type data for a recipe
 * @route POST /api/v1/recipes/batch/ingredients-nutrition
 * @access Private
 */
const addIngredientsNutritionCuisine = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body) as RecipeIngredientsNutritionCuisineData;

    // Start transaction
    const transaction = await transactionManager.start();

    const {
      recipe_id,
      ingredients,
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
      haccp_attributes
    } = sanitizedBody;

    // Common authorization check
    const authResult = checkBatchAuthorization(req, res);
    if (!authResult) return;
    const { userId, organizationId } = authResult;

    // Validate recipe ID
    if (!validateRecipeId(recipe_id, res)) return;

    // Validate recipe exists and user has access
    const recipe = await validateRecipeAccess(recipe_id, organizationId, transaction);
    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Recipe not found",
      });
    }

    // Add ingredients if provided
    if (ingredients && ingredients.length > 0) {
      // First make any existing ingredients inactive
      await RecipeIngredients.update(
        { status: RecipeIngredientsStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      // Create new ingredient records
      const ingredientData = ingredients.map((ingredient) => ({
        recipe_id,
        ingredient_id: ingredient.id,
        ingredient_quantity: ingredient.quantity,
        ingredient_measure: ingredient.measure,
        ingredient_wastage: ingredient.wastage,
        ingredient_cost: ingredient.cost,
        cooking_method: ingredient.cooking_method,
        preparation_method: ingredient.preparation_method,
        status: RecipeIngredientsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));

      await RecipeIngredients.bulkCreate(ingredientData, { transaction });

      // Update ingredient costs timestamp
      await updateRecipeCostTimestamp(recipe_id, transaction);
    }

    // Add nutrition attributes if provided
    if (
      nutrition_attributes && nutrition_attributes.length > 0 ||
      allergen_attributes ||
      cuisine_attributes ||
      dietary_attributes ||
      haccp_attributes
    ) {
      // First make any existing attributes inactive
      await RecipeAttributes.update(
        { status: RecipeAttributesStatus.inactive },
        {
          where: { recipe_id },
          transaction,
        }
      );

      const attributesData = [];

      // Add nutrition attributes
      if (nutrition_attributes && nutrition_attributes.length > 0) {
        const nutritionData = nutrition_attributes.map((attr) => ({
          recipe_id,
          attribute_id: attr.id,
          unit_of_measure: attr.unit_of_measure,
          unit: attr.unit,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          attribute_type: "nutrition",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...nutritionData);
      }

      // Add allergen "contains" attributes
      if (allergen_attributes?.contains && allergen_attributes.contains.length > 0) {
        const allergenContains = allergen_attributes.contains.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "allergen_contains",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...allergenContains);
      }

      // Add allergen "may contain" attributes
      if (allergen_attributes?.may_contain && allergen_attributes.may_contain.length > 0) {
        const allergenMayContain = allergen_attributes.may_contain.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "allergen_may_contain",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...allergenMayContain);
      }

      // Add cuisine attributes
      if (cuisine_attributes && cuisine_attributes.length > 0) {
        const cuisineData = cuisine_attributes.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "cuisine",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...cuisineData);
      }

      // Add dietary attributes
      if (dietary_attributes && dietary_attributes.length > 0) {
        const dietaryData = dietary_attributes.map((attrId) => ({
          recipe_id,
          attribute_id: attrId,
          attribute_type: "dietary",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...dietaryData);
      }

      // Add HACCP attributes
      if (haccp_attributes && haccp_attributes.length > 0) {
        const haccpData = haccp_attributes.map((attr) => ({
          recipe_id,
          attribute_id: attr.id,
          attribute_description: attr.attribute_description,
          use_default: attr.use_default || false,
          attribute_type: "haccp",
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        }));

        attributesData.push(...haccpData);
      }

      // Bulk create all attributes
      if (attributesData.length > 0) {
        await RecipeAttributes.bulkCreate(attributesData, { transaction });
      }

      // Update nutrition values timestamp
      await updateRecipeNutritionTimestamp(recipe_id, transaction);
    }

    // Update recipe timestamp
    await updateRecipeTimestamp(recipe_id, userId, transaction);

    // Create recipe history entry
    const historyData = createHistoryData(
      recipe_id,
      RecipeHistoryAction.updated,
      `Recipe "${recipe.recipe_title}" ingredients, nutrition, and cuisine types were updated.`,
      req,
      organizationId,
      userId
    );
    await createRecipeHistory(historyData, transaction);

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json(
      createSuccessResponse("Recipe ingredients, nutrition, and cuisine data saved successfully", {
        recipe_id
      })
    );
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error adding ingredients and nutrition data"
    );
  }
};

export default {
  addIngredientsNutritionCuisine,
};
