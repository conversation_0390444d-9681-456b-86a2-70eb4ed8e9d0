import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { db } from "../models";
import { RecipeStatus } from "../models/Recipe";

// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;

// Constants for batch processing
export const STEPS_BATCH_SIZE = 5;
export const UPLOADS_BATCH_SIZE = 5;

/**
 * Helper function to check if user can create/update recipes
 */
export const canCreateUpdateRecipes = (userRole: any): boolean => {
  if (!userRole) return false;

  const ADMIN_SIDE_USER = ["admin", "chef", "kitchen_manager", "recipe_manager"];
  return ADMIN_SIDE_USER.includes(userRole.role_name);
};

/**
 * Extract common user data from request
 */
export const extractUserData = (req: Request) => {
  const userId = (req as any).user?.id;
  const organizationId = (req as any).user?.organization_id;
  const userRole = (req as any).user?.roles?.[0];

  return { userId, organizationId, userRole };
};

/**
 * Common authorization check for batch operations
 */
export const checkBatchAuthorization = (req: Request, res: Response): { userId: number; organizationId: string; userRole: any } | null => {
  const { userId, organizationId, userRole } = extractUserData(req);

  if (!userId) {
    res.status(StatusCodes.UNAUTHORIZED).json({
      status: false,
      message: "Unauthorized access",
    });
    return null;
  }

  // Permission check: Only certain roles can create/update recipes
  if (!canCreateUpdateRecipes(userRole)) {
    res.status(StatusCodes.FORBIDDEN).json({
      status: false,
      message: "Permission denied",
    });
    return null;
  }

  return { userId, organizationId, userRole };
};

/**
 * Validate that a recipe exists and user has access to it
 */
export const validateRecipeAccess = async (
  recipeId: number,
  organizationId: string,
  transaction?: any
): Promise<any> => {
  const recipe = await Recipe.findOne({
    where: {
      id: recipeId,
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    },
    transaction,
  });

  return recipe;
};

/**
 * Common response format for successful batch operations
 */
export const createSuccessResponse = (
  message: string,
  data: any,
  statusCode: number = StatusCodes.OK
) => {
  return {
    status: true,
    message,
    data,
  };
};

/**
 * Common response format for creation operations
 */
export const createCreatedResponse = (message: string, data: any) => {
  return createSuccessResponse(message, data, StatusCodes.CREATED);
};

/**
 * Common validation for required recipe ID
 */
export const validateRecipeId = (recipeId: any, res: Response): boolean => {
  if (!recipeId) {
    res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: "Recipe ID is required",
    });
    return false;
  }

  if (isNaN(parseInt(recipeId))) {
    res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: "Recipe ID must be a valid number",
    });
    return false;
  }

  return true;
};

/**
 * Common validation for required recipe title
 */
export const validateRecipeTitle = (recipeTitle: any, res: Response): boolean => {
  if (!recipeTitle) {
    res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: "Recipe title is required",
    });
    return false;
  }

  return true;
};

/**
 * Common function to update recipe timestamp
 */
export const updateRecipeTimestamp = async (
  recipeId: number,
  userId: number,
  transaction?: any
): Promise<void> => {
  await Recipe.update(
    {
      updated_by: userId,
      updated_at: new Date()
    },
    {
      where: { id: recipeId },
      transaction
    }
  );
};

/**
 * Common function to create history data structure
 */
export const createHistoryData = (
  recipeId: number,
  action: string,
  description: string,
  req: Request,
  organizationId: string,
  userId: number
) => {
  return {
    recipe_id: recipeId,
    action,
    description,
    ip_address: req.ip,
    user_agent: req.get("User-Agent") || "",
    organization_id: organizationId,
    created_by: userId,
  };
};
